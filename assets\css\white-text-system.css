/* ===== СИСТЕМА БЕЛОГО ТЕКСТА С ВЫСОКОЙ КОНТРАСТНОСТЬЮ ===== */
/* Обеспечивает читаемость белого текста на всех фонах согласно WCAG 2.1 AAA */

/* ===== ТЕМНЫЕ ФОНЫ ДЛЯ БЕЛОГО ТЕКСТА ===== */
:root {
    /* Темные фоны с высокой контрастностью для белого текста */
    --white-text-bg-primary: #1a3d2e;      /* Темно-зеленый - контраст 12.6:1 */
    --white-text-bg-secondary: #0f2419;    /* Лесной зеленый - контраст 15.8:1 */
    --white-text-bg-tertiary: #2d5a3d;     /* Средний зеленый - контраст 8.4:1 */
    --white-text-bg-navy: #1e3a8a;         /* Темно-синий - контраст 9.2:1 */
    --white-text-bg-charcoal: #2c2c2c;     /* Угольный - контраст 11.9:1 */
    
    /* Градиенты для белого текста */
    --white-text-gradient-green: linear-gradient(135deg, #1a3d2e 0%, #0f2419 100%);
    --white-text-gradient-blue: linear-gradient(135deg, #1e3a8a 0%, #1a3d2e 100%);
    --white-text-gradient-luxury: linear-gradient(135deg, #0f2419 0%, #2d5a3d 50%, #1a3d2e 100%);
    
    /* Полупрозрачные темные фоны */
    --white-text-overlay-dark: rgba(26, 61, 46, 0.95);
    --white-text-overlay-forest: rgba(15, 36, 25, 0.9);
    --white-text-overlay-navy: rgba(30, 58, 138, 0.9);
}

/* ===== БАЗОВЫЕ КЛАССЫ ДЛЯ БЕЛОГО ТЕКСТА ===== */

/* Основной класс для белого текста */
.white-text-container {
    background: var(--white-text-bg-primary);
    color: #ffffff !important;
}

.white-text-container * {
    color: #ffffff !important;
}

/* Вторичный фон для белого текста */
.white-text-secondary {
    background: var(--white-text-bg-secondary);
    color: #ffffff !important;
}

.white-text-secondary * {
    color: #ffffff !important;
}

/* Третичный фон для белого текста */
.white-text-tertiary {
    background: var(--white-text-bg-tertiary);
    color: #ffffff !important;
}

.white-text-tertiary * {
    color: #ffffff !important;
}

/* ===== ИСПРАВЛЕНИЯ ДЛЯ ПРОБЛЕМНЫХ ЭЛЕМЕНТОВ ===== */

/* Карточки с белым текстом */
.luxury-card.white-text,
.card.white-text {
    background: var(--white-text-bg-primary) !important;
    color: #ffffff !important;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.luxury-card.white-text *,
.card.white-text * {
    color: #ffffff !important;
}

/* Формы с белым текстом */
.form-control.white-text {
    background: var(--white-text-bg-tertiary) !important;
    color: #ffffff !important;
    border: 2px solid rgba(212, 175, 55, 0.4) !important;
}

.form-control.white-text::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

.form-control.white-text:focus {
    background: var(--white-text-bg-primary) !important;
    border-color: #d4af37 !important;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3) !important;
}

/* Таблицы с белым текстом */
.table.white-text,
.luxury-table.white-text {
    background: var(--white-text-bg-primary) !important;
    color: #ffffff !important;
}

.table.white-text th,
.table.white-text td,
.luxury-table.white-text th,
.luxury-table.white-text td {
    background: var(--white-text-bg-primary) !important;
    color: #ffffff !important;
    border-color: rgba(212, 175, 55, 0.3) !important;
}

.table.white-text thead th,
.luxury-table.white-text thead th {
    background: var(--white-text-bg-secondary) !important;
    color: #ffffff !important;
}

/* ===== НАВИГАЦИЯ И ХЕДЕРЫ ===== */

/* Навигационная панель */
.navbar.white-text {
    background: var(--white-text-gradient-green) !important;
}

.navbar.white-text .navbar-brand,
.navbar.white-text .nav-link,
.navbar.white-text .navbar-text {
    color: #ffffff !important;
}

/* Заголовки секций */
.section-header.white-text {
    background: var(--white-text-gradient-luxury);
    color: #ffffff !important;
    padding: 2rem;
    text-align: center;
}

.section-header.white-text h1,
.section-header.white-text h2,
.section-header.white-text h3,
.section-header.white-text p {
    color: #ffffff !important;
}

/* ===== КНОПКИ И ИНТЕРАКТИВНЫЕ ЭЛЕМЕНТЫ ===== */

/* Кнопки на темном фоне */
.btn.white-text-bg {
    background: var(--white-text-bg-primary) !important;
    color: #ffffff !important;
    border: 2px solid rgba(212, 175, 55, 0.5);
}

.btn.white-text-bg:hover {
    background: var(--white-text-bg-secondary) !important;
    color: #ffffff !important;
    border-color: #d4af37;
    transform: translateY(-2px);
}

/* Бейджи с белым текстом */
.badge.white-text-bg {
    background: var(--white-text-bg-navy) !important;
    color: #ffffff !important;
}

/* ===== АЛЕРТЫ И УВЕДОМЛЕНИЯ ===== */

/* Алерты с белым текстом */
.alert.white-text {
    background: var(--white-text-bg-tertiary) !important;
    color: #ffffff !important;
    border: 1px solid rgba(212, 175, 55, 0.4);
}

.alert.white-text .alert-heading {
    color: #ffffff !important;
}

/* ===== МОДАЛЬНЫЕ ОКНА ===== */

/* Модальные окна с белым текстом */
.modal-content.white-text {
    background: var(--white-text-bg-primary) !important;
    color: #ffffff !important;
}

.modal-header.white-text {
    background: var(--white-text-bg-secondary) !important;
    color: #ffffff !important;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.modal-header.white-text .modal-title,
.modal-header.white-text .btn-close {
    color: #ffffff !important;
}

.modal-body.white-text,
.modal-footer.white-text {
    background: var(--white-text-bg-primary) !important;
    color: #ffffff !important;
}

/* ===== СПЕЦИАЛЬНЫЕ УТИЛИТЫ ===== */

/* Принудительный белый текст */
.force-white-text {
    color: #ffffff !important;
}

.force-white-text * {
    color: #ffffff !important;
}

/* Темный фон для белого текста */
.dark-bg-for-white-text {
    background: var(--white-text-bg-primary) !important;
}

/* Градиентный фон для белого текста */
.gradient-bg-for-white-text {
    background: var(--white-text-gradient-luxury) !important;
}

/* ===== АДАПТИВНОСТЬ ===== */

/* Мобильные устройства */
@media (max-width: 768px) {
    .white-text-container,
    .white-text-secondary,
    .white-text-tertiary {
        padding: 1rem;
    }
    
    .section-header.white-text {
        padding: 1.5rem 1rem;
    }
}

/* Высокая контрастность */
@media (prefers-contrast: high) {
    :root {
        --white-text-bg-primary: #000000;
        --white-text-bg-secondary: #1a1a1a;
        --white-text-bg-tertiary: #2d2d2d;
    }
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
    .white-text-container,
    .white-text-secondary,
    .white-text-tertiary {
        background: var(--white-text-bg-secondary);
    }
}
