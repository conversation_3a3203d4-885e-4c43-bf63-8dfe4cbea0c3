/* ===== GREENCHAIN ECOFUND - ПРЕМИУМ ИНВЕСТИЦИОННАЯ ПЛАТФОРМА ===== */

/* ===== CSS ПЕРЕМЕННЫЕ ===== */
:root {
    /* Основная цветовая палитра */
    --primary-50: #f0f9f4;
    --primary-100: #dcf4e6;
    --primary-200: #bbe8d0;
    --primary-300: #86d5b0;
    --primary-400: #4abb87;
    --primary-500: #22a05e;
    --primary-600: #16844a;
    --primary-700: #13693c;
    --primary-800: #125432;
    --primary-900: #0f452a;

    /* Вторичные цвета */
    --secondary-50: #eff6ff;
    --secondary-100: #dbeafe;
    --secondary-200: #bfdbfe;
    --secondary-300: #93c5fd;
    --secondary-400: #60a5fa;
    --secondary-500: #3b82f6;
    --secondary-600: #2563eb;
    --secondary-700: #1d4ed8;
    --secondary-800: #1e40af;
    --secondary-900: #1e3a8a;

    /* Акцентные цвета */
    --accent-50: #f0fdfa;
    --accent-100: #ccfbf1;
    --accent-200: #99f6e4;
    --accent-300: #5eead4;
    --accent-400: #2dd4bf;
    --accent-500: #14b8a6;
    --accent-600: #0d9488;
    --accent-700: #0f766e;
    --accent-800: #115e59;
    --accent-900: #134e4a;

    /* Статусные цвета */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;

    /* Нейтральные цвета */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --white: #ffffff;
    --black: #000000;

    /* Основные цвета для использования */
    --primary: var(--primary-600);
    --primary-light: var(--primary-500);
    --primary-dark: var(--primary-700);
    --secondary: var(--secondary-600);
    --accent: var(--accent-600);
    --success: var(--success-600);
    --warning: var(--warning-500);
    --danger: var(--danger-600);

    /* Градиенты */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-500) 50%, var(--accent-500) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-600) 0%, var(--secondary-500) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-600) 25%, var(--accent-600) 75%, var(--secondary-600) 100%);
    --gradient-card: linear-gradient(145deg, var(--white) 0%, var(--gray-50) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Тени */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Цветные тени */
    --shadow-primary: 0 10px 15px -3px rgba(22, 132, 74, 0.1), 0 4px 6px -2px rgba(22, 132, 74, 0.05);
    --shadow-secondary: 0 10px 15px -3px rgba(37, 99, 235, 0.1), 0 4px 6px -2px rgba(37, 99, 235, 0.05);
    --shadow-success: 0 10px 15px -3px rgba(22, 163, 74, 0.1), 0 4px 6px -2px rgba(22, 163, 74, 0.05);

    /* Радиусы */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Переходы */
    --transition-none: none;
    --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 500ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Типографика */
    --font-family-sans: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    --font-family-mono: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;

    /* Размеры шрифтов */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Высота строк */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Веса шрифтов */
    --font-thin: 100;
    --font-extralight: 200;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;

    /* Размеры */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;

    /* Z-индексы */
    --z-0: 0;
    --z-10: 10;
    --z-20: 20;
    --z-30: 30;
    --z-40: 40;
    --z-50: 50;
    --z-auto: auto;

    /* Специальные значения */
    --navbar-height: 80px;
    --sidebar-width: 280px;
    --footer-height: 200px;
}

/* ===== СБРОС И БАЗОВЫЕ СТИЛИ ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--gray-700);
    background: linear-gradient(135deg, var(--gray-50) 0%, #e0f2fe 50%, var(--primary-50) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Улучшенная прокрутка */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Выделение текста */
::selection {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

::-moz-selection {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

/* Фокус */
:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Ссылки */
a {
    color: var(--primary-600);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* Изображения */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Кнопки */
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    overflow: visible;
    text-transform: none;
    -webkit-appearance: button;
    cursor: pointer;
}

button::-moz-focus-inner {
    border-style: none;
    padding: 0;
}

button:-moz-focusring {
    outline: 1px dotted ButtonText;
}

/* Формы */
input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
}

input[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
}

input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

/* Таблицы */
table {
    border-collapse: collapse;
    border-spacing: 0;
}

th,
td {
    text-align: left;
    vertical-align: top;
}

/* Списки */
ul,
ol {
    list-style: none;
}

/* Заголовки */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    color: var(--gray-900);
    margin-bottom: 0.5em;
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

/* Параграфы */
p {
    margin-bottom: 1rem;
    line-height: var(--leading-relaxed);
}

/* Утилиты для скрытия */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Контейнеры */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 1rem;
}

.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

/* ===== ПРЕЛОАДЕР ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.spinner {
    width: 60px;
    height: 60px;
    position: relative;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.double-bounce1, .double-bounce2 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--white);
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: sk-bounce 2.0s infinite ease-in-out;
}

.double-bounce2 {
    animation-delay: -1.0s;
}

@keyframes sk-bounce {
    0%, 100% {
        transform: scale(0);
    } 50% {
        transform: scale(1);
    }
}

/* ===== НАВИГАЦИЯ ===== */
.header {
    background: var(--gradient-hero);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: var(--z-50);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar {
    height: var(--navbar-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    position: relative;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: var(--font-bold);
    font-size: var(--text-xl);
    color: var(--white) !important;
    text-decoration: none;
    transition: var(--transition);
    z-index: var(--z-10);
}

.navbar-brand:hover {
    transform: scale(1.05);
    color: var(--white) !important;
}

.navbar-brand .logo {
    height: 40px;
    width: 40px;
    margin-right: 0.75rem;
    border-radius: var(--radius-lg);
    background: var(--white);
    padding: 0.5rem;
    box-shadow: var(--shadow);
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    padding: 0.75rem 1rem !important;
    border-radius: var(--radius-lg);
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--white) !important;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.nav-link i {
    font-size: 1rem;
}

/* Мобильное меню */
.navbar-toggler {
    display: none;
    background: transparent;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    padding: 0.5rem;
    border-radius: var(--radius-lg);
    transition: var(--transition);
    cursor: pointer;
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.navbar-collapse {
    display: flex;
    align-items: center;
}

/* Выпадающие меню */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: var(--z-50);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-600);
    transform: translateX(4px);
}

.dropdown-item i {
    font-size: 1rem;
    color: var(--gray-400);
    transition: var(--transition);
}

.dropdown-item:hover i {
    color: var(--primary-600);
}

/* ===== КНОПКИ ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-family: var(--font-family-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    line-height: 1;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 2px solid transparent;
    border-radius: var(--radius-xl);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    box-shadow: var(--shadow);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
    z-index: 1;
}

.btn:hover::before {
    left: 100%;
}

.btn > * {
    position: relative;
    z-index: 2;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

.btn:focus {
    outline: none;
    box-shadow: var(--shadow-lg), 0 0 0 3px rgba(22, 132, 74, 0.1);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: var(--shadow) !important;
}

/* Варианты кнопок */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: transparent;
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    box-shadow: var(--shadow-xl), var(--shadow-primary);
    color: var(--white);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--white);
    border-color: transparent;
    box-shadow: var(--shadow-secondary);
}

.btn-secondary:hover {
    box-shadow: var(--shadow-xl), var(--shadow-secondary);
    color: var(--white);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--white);
    border-color: transparent;
    box-shadow: var(--shadow-success);
}

.btn-success:hover {
    box-shadow: var(--shadow-xl), var(--shadow-success);
    color: var(--white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%);
    color: var(--white);
    border-color: transparent;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-500) 100%);
    color: var(--white);
    border-color: transparent;
}

.btn-light {
    background: var(--white);
    color: var(--gray-700);
    border-color: var(--gray-200);
    box-shadow: var(--shadow);
}

.btn-light:hover {
    background: var(--gray-50);
    color: var(--gray-800);
}

/* Контурные кнопки */
.btn-outline-primary {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
    box-shadow: none;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: transparent;
    box-shadow: var(--shadow-primary);
}

/* Размеры кнопок */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--text-xs);
    min-height: 36px;
    border-radius: var(--radius-lg);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: var(--text-base);
    min-height: 52px;
    border-radius: var(--radius-2xl);
}

.btn-xl {
    padding: 1.25rem 2.5rem;
    font-size: var(--text-lg);
    min-height: 60px;
    border-radius: var(--radius-2xl);
}

/* Специальные кнопки */
.btn-icon {
    width: 44px;
    height: 44px;
    padding: 0;
    border-radius: var(--radius-full);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-floating {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-40);
}

.btn-floating:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow: var(--shadow-2xl);
}

/* ===== КАРТОЧКИ ===== */
.card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: var(--gradient-card);
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem;
    font-weight: var(--font-semibold);
    color: var(--gray-800);
    position: relative;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

.card-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    line-height: var(--leading-tight);
}

.card-subtitle {
    color: var(--gray-600);
    margin-bottom: 1rem;
    font-size: var(--text-sm);
}

.card-text {
    color: var(--gray-700);
    line-height: var(--leading-relaxed);
}

/* Специальные карточки */
.card-investment {
    background: var(--gradient-card);
    border: 2px solid var(--primary-200);
    position: relative;
    overflow: hidden;
}

.card-investment::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, var(--primary-100) 0%, transparent 70%);
    opacity: 0.5;
    pointer-events: none;
}

.card-stats {
    background: var(--white);
    border: none;
    box-shadow: var(--shadow-lg);
    text-align: center;
}

.card-stats .card-body {
    padding: 2rem 1.5rem;
}

.card-stats .stats-number {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    margin-bottom: 0.5rem;
    display: block;
}

.card-stats .stats-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-medium);
}

.card-feature {
    text-align: center;
    border: 2px solid transparent;
    transition: var(--transition);
}

.card-feature:hover {
    border-color: var(--primary-200);
    background: var(--primary-50);
}

.card-feature .feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: var(--text-2xl);
    box-shadow: var(--shadow-primary);
}

.card-pricing {
    position: relative;
    border: 2px solid var(--gray-200);
    transition: var(--transition);
}

.card-pricing.featured {
    border-color: var(--primary-500);
    transform: scale(1.05);
    z-index: 1;
}

.card-pricing.featured::before {
    content: 'Популярный';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    font-size: var(--text-xs);
    font-weight: var(--font-bold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-pricing:hover {
    border-color: var(--primary-400);
    box-shadow: var(--shadow-xl);
}

.pricing-price {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    margin-bottom: 0.5rem;
}

.pricing-period {
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.pricing-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--gray-700);
}

.pricing-features li::before {
    content: '✓';
    color: var(--success-600);
    font-weight: var(--font-bold);
    font-size: var(--text-lg);
}

/* Адаптивные карточки */
.card-deck {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card-columns {
    column-count: 3;
    column-gap: 1.5rem;
    column-fill: balance;
}

.card-columns .card {
    display: inline-block;
    width: 100%;
    margin-bottom: 1.5rem;
    break-inside: avoid;
}

.balance-badge {
    background: var(--gradient-success);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    margin-left: 0.5rem;
    box-shadow: var(--shadow-success);
}

/* ===== ФОРМЫ ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: var(--font-semibold);
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.875rem 1rem;
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--gray-700);
    background-color: var(--white);
    background-clip: padding-box;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-xl);
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: var(--shadow), 0 0 0 3px rgba(22, 132, 74, 0.1);
    background-color: var(--white);
}

.form-control::placeholder {
    color: var(--gray-400);
    opacity: 1;
}

.form-control:disabled {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    opacity: 1;
    color: var(--gray-500);
}

/* Размеры форм */
.form-control-sm {
    padding: 0.5rem 0.75rem;
    font-size: var(--text-sm);
    border-radius: var(--radius-lg);
}

.form-control-lg {
    padding: 1rem 1.25rem;
    font-size: var(--text-lg);
    border-radius: var(--radius-2xl);
}

/* Select */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    appearance: none;
}

.form-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23168444' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
}

/* Input groups */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group > .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--gray-600);
    text-align: center;
    white-space: nowrap;
    background-color: var(--gray-100);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-xl);
    transition: var(--transition);
}

/* Checkbox и Radio */
.form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.75em;
    margin-bottom: 0.125rem;
}

.form-check-input {
    width: 1.25em;
    height: 1.25em;
    margin-top: 0.125em;
    margin-left: -1.75em;
    vertical-align: top;
    background-color: var(--white);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 2px solid var(--gray-300);
    appearance: none;
    transition: var(--transition);
    cursor: pointer;
}

.form-check-input[type="checkbox"] {
    border-radius: var(--radius-md);
}

.form-check-input[type="radio"] {
    border-radius: var(--radius-full);
}

.form-check-input:focus {
    border-color: var(--primary-500);
    outline: none;
    box-shadow: 0 0 0 3px rgba(22, 132, 74, 0.1);
}

.form-check-input:checked {
    background-color: var(--primary-600);
    border-color: var(--primary-600);
}

.form-check-input:checked[type="checkbox"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

.form-check-input:checked[type="radio"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='white'/%3e%3c/svg%3e");
}

.form-check-label {
    color: var(--gray-700);
    cursor: pointer;
    font-weight: var(--font-medium);
}

/* Валидация форм */
.form-text {
    margin-top: 0.25rem;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: var(--text-sm);
    color: var(--danger-600);
    font-weight: var(--font-medium);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: var(--danger-500);
    box-shadow: var(--shadow), 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.was-validated .form-control:invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: var(--success-500);
    box-shadow: var(--shadow), 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* ===== СПЕЦИАЛЬНЫЕ КОМПОНЕНТЫ ===== */

/* Hero секция */
.hero-section {
    background: var(--gradient-hero);
    color: var(--white);
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    margin-bottom: 1.5rem;
    line-height: var(--leading-tight);
}

.hero-subtitle {
    font-size: var(--text-xl);
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Статистика */
.stats-section {
    background: var(--white);
    padding: 3rem 0;
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 10;
    margin-top: -2rem;
    border-radius: var(--radius-3xl) var(--radius-3xl) 0 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--radius-2xl);
    background: var(--gradient-card);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-semibold);
}

/* УДАЛЕНО: Стили инвестиционных пакетов */

.package-name {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.package-rate {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    margin-bottom: 0.5rem;
}

.package-period {
    color: var(--gray-600);
    margin-bottom: 2rem;
    font-size: var(--text-sm);
}

.package-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.package-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--gray-700);
}

.package-features li::before {
    content: '✓';
    color: var(--success-600);
    font-weight: var(--font-bold);
    font-size: var(--text-lg);
    flex-shrink: 0;
}

/* Таблицы */
.table-responsive {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 2rem;
}

.table {
    margin-bottom: 0;
    color: var(--gray-700);
}

.table th {
    background: var(--gray-50);
    border-bottom: 2px solid var(--gray-200);
    font-weight: var(--font-semibold);
    color: var(--gray-800);
    padding: 1rem;
    text-transform: uppercase;
    font-size: var(--text-xs);
    letter-spacing: 0.5px;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Бейджи и метки */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    line-height: 1;
    color: var(--white);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-primary);
}

.badge-secondary {
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-secondary);
}

.badge-success {
    background: var(--gradient-success);
    box-shadow: var(--shadow-success);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-500) 100%);
}

.badge-light {
    background: var(--gray-100);
    color: var(--gray-700);
}

.badge-dark {
    background: var(--gray-800);
}

/* Алерты */
.alert {
    position: relative;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--radius-xl);
    font-weight: var(--font-medium);
}

.alert-primary {
    color: var(--primary-800);
    background-color: var(--primary-50);
    border-color: var(--primary-200);
}

.alert-secondary {
    color: var(--secondary-800);
    background-color: var(--secondary-50);
    border-color: var(--secondary-200);
}

.alert-success {
    color: var(--success-800);
    background-color: var(--success-50);
    border-color: var(--success-200);
}

.alert-warning {
    color: var(--warning-800);
    background-color: var(--warning-50);
    border-color: var(--warning-200);
}

.alert-danger {
    color: var(--danger-800);
    background-color: var(--danger-50);
    border-color: var(--danger-200);
}

.alert-dismissible {
    padding-right: 3rem;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem;
    background: none;
    border: none;
    font-size: var(--text-lg);
    color: inherit;
    opacity: 0.5;
    cursor: pointer;
}

.alert-dismissible .btn-close:hover {
    opacity: 1;
}

/* ===== УТИЛИТАРНЫЕ КЛАССЫ ===== */

/* Отображение */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-stretch { align-items: stretch !important; }

/* Позиционирование */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Размеры */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* Отступы */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }
.m-auto { margin: auto !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }
.mt-auto { margin-top: auto !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }
.mb-auto { margin-bottom: auto !important; }

.ml-0, .ms-0 { margin-left: 0 !important; }
.ml-1, .ms-1 { margin-left: 0.25rem !important; }
.ml-2, .ms-2 { margin-left: 0.5rem !important; }
.ml-3, .ms-3 { margin-left: 1rem !important; }
.ml-4, .ms-4 { margin-left: 1.5rem !important; }
.ml-5, .ms-5 { margin-left: 3rem !important; }
.ml-auto, .ms-auto { margin-left: auto !important; }

.mr-0, .me-0 { margin-right: 0 !important; }
.mr-1, .me-1 { margin-right: 0.25rem !important; }
.mr-2, .me-2 { margin-right: 0.5rem !important; }
.mr-3, .me-3 { margin-right: 1rem !important; }
.mr-4, .me-4 { margin-right: 1.5rem !important; }
.mr-5, .me-5 { margin-right: 3rem !important; }
.mr-auto, .me-auto { margin-right: auto !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !important; }
.mx-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }
.mx-3 { margin-left: 1rem !important; margin-right: 1rem !important; }
.mx-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !important; }
.mx-5 { margin-left: 3rem !important; margin-right: 3rem !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !important; }
.my-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }
.my-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
.my-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }
.my-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }
.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

/* Padding */
.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 1rem !important; }
.pt-4 { padding-top: 1.5rem !important; }
.pt-5 { padding-top: 3rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 1rem !important; }
.pb-4 { padding-bottom: 1.5rem !important; }
.pb-5 { padding-bottom: 3rem !important; }

.pl-0, .ps-0 { padding-left: 0 !important; }
.pl-1, .ps-1 { padding-left: 0.25rem !important; }
.pl-2, .ps-2 { padding-left: 0.5rem !important; }
.pl-3, .ps-3 { padding-left: 1rem !important; }
.pl-4, .ps-4 { padding-left: 1.5rem !important; }
.pl-5, .ps-5 { padding-left: 3rem !important; }

.pr-0, .pe-0 { padding-right: 0 !important; }
.pr-1, .pe-1 { padding-right: 0.25rem !important; }
.pr-2, .pe-2 { padding-right: 0.5rem !important; }
.pr-3, .pe-3 { padding-right: 1rem !important; }
.pr-4, .pe-4 { padding-right: 1.5rem !important; }
.pr-5, .pe-5 { padding-right: 3rem !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
.px-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
.px-5 { padding-left: 3rem !important; padding-right: 3rem !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
.py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

/* Текст */
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.fw-light { font-weight: var(--font-light) !important; }
.fw-normal { font-weight: var(--font-normal) !important; }
.fw-medium { font-weight: var(--font-medium) !important; }
.fw-semibold { font-weight: var(--font-semibold) !important; }
.fw-bold { font-weight: var(--font-bold) !important; }

.fs-1 { font-size: var(--text-5xl) !important; }
.fs-2 { font-size: var(--text-4xl) !important; }
.fs-3 { font-size: var(--text-3xl) !important; }
.fs-4 { font-size: var(--text-2xl) !important; }
.fs-5 { font-size: var(--text-xl) !important; }
.fs-6 { font-size: var(--text-lg) !important; }

.text-muted { color: var(--gray-600) !important; }
.text-primary { color: var(--primary-600) !important; }
.text-secondary { color: var(--secondary-600) !important; }
.text-success { color: var(--success-600) !important; }
.text-warning { color: var(--warning-600) !important; }
.text-danger { color: var(--danger-600) !important; }
.text-white { color: var(--white) !important; }
.text-dark { color: var(--gray-900) !important; }

/* Фон */
.bg-primary { background-color: var(--primary-600) !important; }
.bg-secondary { background-color: var(--secondary-600) !important; }
.bg-success { background-color: var(--success-600) !important; }
.bg-warning { background-color: var(--warning-500) !important; }
.bg-danger { background-color: var(--danger-600) !important; }
.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-800) !important; }
.bg-white { background-color: var(--white) !important; }
.bg-transparent { background-color: transparent !important; }

.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-secondary { background: var(--gradient-secondary) !important; }
.bg-gradient-success { background: var(--gradient-success) !important; }

/* Границы */
.border { border: 1px solid var(--gray-200) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--gray-200) !important; }
.border-bottom { border-bottom: 1px solid var(--gray-200) !important; }
.border-left { border-left: 1px solid var(--gray-200) !important; }
.border-right { border-right: 1px solid var(--gray-200) !important; }

.border-primary { border-color: var(--primary-300) !important; }
.border-secondary { border-color: var(--secondary-300) !important; }
.border-success { border-color: var(--success-300) !important; }
.border-warning { border-color: var(--warning-300) !important; }
.border-danger { border-color: var(--danger-300) !important; }

.rounded { border-radius: var(--radius) !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-3xl { border-radius: var(--radius-3xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* Тени */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* Overflow */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

/* ===== АНИМАЦИИ ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.animate-fadeIn { animation: fadeIn 0.6s ease-out; }
.animate-fadeInUp { animation: fadeInUp 0.6s ease-out; }
.animate-fadeInDown { animation: fadeInDown 0.6s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.6s ease-out; }
.animate-slideInRight { animation: slideInRight 0.6s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-bounce { animation: bounce 1s infinite; }

/* ===== ДОСТУПНОСТЬ ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Скрытие для скринридеров */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Фокус для клавиатурной навигации */
.focus-visible:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* ===== ПЕЧАТЬ ===== */
@media print {
    *,
    *::before,
    *::after {
        text-shadow: none !important;
        box-shadow: none !important;
    }

    a:not(.btn) {
        text-decoration: underline;
    }

    .btn {
        border: 1px solid #000;
    }

    .navbar,
    .sidebar,
    .footer {
        display: none;
    }

    .container {
        max-width: none;
        width: auto;
    }
}

/* Dropdown */
.dropdown-menu {
    background: white;
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.dropdown-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 20px;
    margin-right: var(--spacing-sm);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: var(--spacing-xl) 0;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    background: white;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: var(--spacing-lg);
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-xl);
}

.card-gradient {
    background: var(--card-gradient);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* Forms */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 99, 132, 0.1) 100%);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: var(--font-size-xl);
}

.stats-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stats-label {
    color: #6c757d;
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* Progress Bars */
.progress {
    height: 10px;
    border-radius: var(--border-radius-lg);
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
    transition: width 0.6s ease;
}

/* Tables */
.table {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: var(--spacing-lg);
}

.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-color: #f8f9fa;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(46, 139, 87, 0.05);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.footer-widget {
    margin-bottom: var(--spacing-xl);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: white;
}

.widget-text {
    color: rgba(255,255,255,0.8);
    line-height: 1.7;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition-normal);
}

.footer-links a:hover {
    color: white;
    padding-left: var(--spacing-sm);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.footer-divider {
    border-color: rgba(255,255,255,0.2);
    margin: var(--spacing-xl) 0;
}

.copyright {
    color: rgba(255,255,255,0.7);
    margin: 0;
}

.footer-stats {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: flex-end;
}

.stat-item {
    color: rgba(255,255,255,0.8);
    font-size: var(--font-size-sm);
}

.stat-item i {
    margin-right: var(--spacing-xs);
}

/* Back to Top */
.btn-back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-lg);
    display: none;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
    z-index: 1000;
}

.btn-back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Live Chat */
.live-chat-widget {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 1000;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    font-size: var(--font-size-xl);
}

.chat-toggle:hover {
    transform: scale(1.1);
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 300px;
    height: 400px;
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: var(--primary-gradient);
    color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-messages {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

.message {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    max-width: 80%;
}

.bot-message {
    background: var(--light-color);
    align-self: flex-start;
}

.user-message {
    background: var(--primary-color);
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.chat-input {
    display: flex;
    padding: var(--spacing-md);
    border-top: 1px solid #e9ecef;
}

.chat-input input {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm);
    margin-right: var(--spacing-sm);
}

.chat-input button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: var(--primary-gradient);
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, var(--primary-gradient) border-box;
}

/* Responsive helpers */
.d-mobile-none {
    display: block;
}

@media (max-width: 767.98px) {
    .d-mobile-none {
        display: none !important;
    }
}
