/* ===== РОСКОШНЫЕ ФОНОВЫЕ ИЗОБРАЖЕНИЯ И ТЕКСТУРЫ ===== */

/* ===== ПРИРОДНЫЕ ТЕКСТУРЫ ===== */
.bg-luxury-wood {
    background-image: 
        linear-gradient(45deg, rgba(139, 115, 85, 0.1) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(139, 115, 85, 0.1) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(139, 115, 85, 0.1) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(139, 115, 85, 0.1) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.bg-luxury-marble {
    background: 
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(26, 61, 46, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
        linear-gradient(135deg, #faf8f3 0%, #f8f6f0 100%);
}

.bg-luxury-stone {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(139, 115, 85, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(160, 149, 107, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #f8f6f0 0%, #faf8f3 100%);
}

/* ===== ПРЕМИУМ ГРАДИЕНТЫ ===== */
.bg-luxury-hero {
    background: 
        linear-gradient(135deg, rgba(26, 61, 46, 0.95) 0%, rgba(45, 90, 61, 0.9) 70%, rgba(212, 175, 55, 0.1) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="luxury-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23d4af37" opacity="0.1"/><circle cx="5" cy="5" r="0.5" fill="%23d4af37" opacity="0.05"/><circle cx="15" cy="15" r="0.5" fill="%23d4af37" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23luxury-pattern)"/></svg>');
}

.bg-luxury-investment {
    background: 
        linear-gradient(145deg, rgba(250, 248, 243, 0.95) 0%, rgba(248, 246, 240, 0.9) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="investment-pattern" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M30 10 L40 20 L30 30 L20 20 Z" fill="%23d4af37" opacity="0.03"/><circle cx="15" cy="45" r="2" fill="%231a3d2e" opacity="0.02"/><circle cx="45" cy="15" r="2" fill="%231a3d2e" opacity="0.02"/></pattern></defs><rect width="60" height="60" fill="url(%23investment-pattern)"/></svg>');
}

.bg-luxury-profile {
    background: 
        radial-gradient(ellipse at top, rgba(212, 175, 55, 0.1) 0%, transparent 70%),
        radial-gradient(ellipse at bottom, rgba(26, 61, 46, 0.05) 0%, transparent 70%),
        linear-gradient(135deg, #faf8f3 0%, #f8f6f0 100%);
}

/* ===== АНИМИРОВАННЫЕ ФОНЫ ===== */
.bg-luxury-animated {
    background: 
        linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.05) 50%, transparent 70%),
        linear-gradient(135deg, #1a3d2e 0%, #2d5a3d 100%);
    background-size: 200% 200%;
    animation: luxuryShift 8s ease-in-out infinite;
}

@keyframes luxuryShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.bg-luxury-particles {
    position: relative;
    overflow: hidden;
}

.bg-luxury-particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(26, 61, 46, 0.1) 0%, transparent 30%),
        radial-gradient(circle at 40% 70%, rgba(212, 175, 55, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 70% 30%, rgba(26, 61, 46, 0.05) 0%, transparent 30%);
    animation: floatParticles 15s ease-in-out infinite;
    pointer-events: none;
}

@keyframes floatParticles {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

/* ===== СПЕЦИАЛЬНЫЕ СЕКЦИИ ===== */
.luxury-section-divider {
    height: 100px;
    background: 
        linear-gradient(to right, transparent 0%, rgba(212, 175, 55, 0.3) 50%, transparent 100%),
        linear-gradient(135deg, #faf8f3 0%, #f8f6f0 100%);
    position: relative;
}

.luxury-section-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--luxury-gold), transparent);
}

.luxury-section-divider::after {
    content: '◆';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--luxury-gold);
    font-size: 1.2rem;
    background: var(--luxury-cream);
    padding: 0 10px;
}

/* ===== КАРТОЧКИ С ФОНОВЫМИ ИЗОБРАЖЕНИЯМИ ===== */
.luxury-card-skyscraper {
    background: 
        linear-gradient(135deg, rgba(26, 61, 46, 0.9) 0%, rgba(45, 90, 61, 0.8) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="20" y="10" width="8" height="80" fill="%23d4af37" opacity="0.1"/><rect x="35" y="5" width="10" height="85" fill="%23d4af37" opacity="0.15"/><rect x="50" y="15" width="6" height="75" fill="%23d4af37" opacity="0.1"/><rect x="65" y="8" width="12" height="82" fill="%23d4af37" opacity="0.12"/><rect x="0" y="85" width="100" height="15" fill="%231a3d2e" opacity="0.2"/></svg>');
    background-size: cover;
    color: var(--luxury-cream);
}

.luxury-card-nature {
    background: 
        linear-gradient(135deg, rgba(250, 248, 243, 0.95) 0%, rgba(248, 246, 240, 0.9) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="30" r="8" fill="%2316844a" opacity="0.1"/><circle cx="25" cy="25" r="6" fill="%2316844a" opacity="0.08"/><circle cx="15" cy="25" r="5" fill="%2316844a" opacity="0.06"/><rect x="18" y="38" width="4" height="15" fill="%238b7355" opacity="0.1"/><circle cx="70" cy="40" r="10" fill="%2316844a" opacity="0.12"/><circle cx="75" cy="35" r="8" fill="%2316844a" opacity="0.1"/><circle cx="65" cy="35" r="6" fill="%2316844a" opacity="0.08"/><rect x="68" y="50" width="4" height="18" fill="%238b7355" opacity="0.1"/><path d="M40 60 Q50 50 60 60 Q50 70 40 60" fill="%2316844a" opacity="0.05"/></svg>');
    background-size: cover;
}

.luxury-card-solar {
    background: 
        linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="10" y="40" width="25" height="15" fill="%23d4af37" opacity="0.1" rx="2"/><rect x="40" y="35" width="25" height="15" fill="%23d4af37" opacity="0.12" rx="2"/><rect x="70" y="30" width="25" height="15" fill="%23d4af37" opacity="0.1" rx="2"/><rect x="25" y="60" width="25" height="15" fill="%23d4af37" opacity="0.08" rx="2"/><rect x="55" y="55" width="25" height="15" fill="%23d4af37" opacity="0.1" rx="2"/><circle cx="50" cy="20" r="8" fill="%23ffd700" opacity="0.2"/><path d="M50 5 L52 15 L50 12 L48 15 Z" fill="%23ffd700" opacity="0.15"/><path d="M65 20 L55 22 L58 20 L55 18 Z" fill="%23ffd700" opacity="0.15"/><path d="M50 35 L48 25 L50 28 L52 25 Z" fill="%23ffd700" opacity="0.15"/><path d="M35 20 L45 18 L42 20 L45 22 Z" fill="%23ffd700" opacity="0.15"/></svg>');
    background-size: cover;
}

/* ===== ИНТЕРАКТИВНЫЕ ЭФФЕКТЫ ===== */
.luxury-hover-lift {
    transition: var(--transition-luxury);
}

.luxury-hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-luxury-lg);
}

.luxury-hover-glow {
    transition: var(--transition-luxury);
    position: relative;
}

.luxury-hover-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--luxury-gold), var(--luxury-bright-gold), var(--luxury-gold));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: var(--transition-luxury);
}

.luxury-hover-glow:hover::before {
    opacity: 0.3;
}

.luxury-hover-scale {
    transition: var(--transition-luxury);
}

.luxury-hover-scale:hover {
    transform: scale(1.05);
}

/* ===== ДЕКОРАТИВНЫЕ ЭЛЕМЕНТЫ ===== */
.luxury-ornament {
    position: relative;
}

.luxury-ornament::before {
    content: '◆ ◆ ◆';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--luxury-gold);
    font-size: 0.8rem;
    letter-spacing: 10px;
}

.luxury-border-gold {
    border: 2px solid var(--luxury-gold) !important;
    position: relative;
}

.luxury-border-gold::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, var(--luxury-gold), transparent, var(--luxury-gold));
    border-radius: inherit;
    z-index: -1;
    opacity: 0.5;
}

/* ===== СПЕЦИАЛЬНЫЕ АНИМАЦИИ ===== */
.luxury-fade-in {
    animation: luxuryFadeIn 1s ease-out;
}

@keyframes luxuryFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.luxury-slide-in-left {
    animation: luxurySlideInLeft 0.8s ease-out;
}

@keyframes luxurySlideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.luxury-slide-in-right {
    animation: luxurySlideInRight 0.8s ease-out;
}

@keyframes luxurySlideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
