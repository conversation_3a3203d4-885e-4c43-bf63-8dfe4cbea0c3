<?php
$page_title = "Реферальная программа";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();
$referral_stats = getReferralStats($user['id']);
$referral_tree = getReferralTree($user['id']);
$referral_earnings = getReferralEarnings($user['id']);
$referral_rates = json_decode(getSetting('referral_rates', '[5, 3, 1]'), true);
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-users text-primary"></i> Реферальная программа
                </h2>
                <p class="page-subtitle">Приглашайте друзей и получайте дополнительный доход</p>
            </div>
        </div>
    </div>
    
    <!-- Статистика -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $referral_stats['total_referrals']; ?></div>
                    <div class="stats-label">Всего рефералов</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($referral_stats['total_earnings']); ?></div>
                    <div class="stats-label">Общий заработок</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $referral_stats['active_referrals']; ?></div>
                    <div class="stats-label">Активных рефералов</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($referral_stats['today_earnings']); ?></div>
                    <div class="stats-label">Заработок сегодня</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Реферальная ссылка -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link"></i> Ваша реферальная ссылка
                    </h5>
                </div>
                <div class="card-body">
                    <div class="referral-link-section">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="referralLink" 
                                   value="<?php echo SITE_URL; ?>/index.php?page=register&ref=<?php echo $user['referral_code']; ?>" 
                                   readonly>
                            <button class="btn btn-primary copy-btn" type="button" 
                                    data-copy="<?php echo SITE_URL; ?>/index.php?page=register&ref=<?php echo $user['referral_code']; ?>">
                                <i class="fas fa-copy"></i> Копировать
                            </button>
                        </div>
                        
                        <div class="social-share">
                            <h6>Поделиться в социальных сетях:</h6>
                            <div class="social-buttons">
                                <a href="#" class="btn btn-outline-primary btn-sm social-btn" 
                                   onclick="shareToTelegram()" title="Telegram">
                                    <i class="fab fa-telegram"></i>
                                </a>
                                <a href="#" class="btn btn-outline-info btn-sm social-btn" 
                                   onclick="shareToTwitter()" title="Twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="btn btn-outline-primary btn-sm social-btn" 
                                   onclick="shareToFacebook()" title="Facebook">
                                    <i class="fab fa-facebook"></i>
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm social-btn" 
                                   onclick="shareToWhatsApp()" title="WhatsApp">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                <a href="#" class="btn btn-outline-dark btn-sm social-btn" 
                                   onclick="shareByEmail()" title="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Структура рефералов -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sitemap"></i> Структура рефералов
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($referral_tree)): ?>
                        <div class="empty-state">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>У вас пока нет рефералов</h5>
                            <p class="text-muted">Поделитесь реферальной ссылкой с друзьями и начните зарабатывать!</p>
                        </div>
                    <?php else: ?>
                        <div class="referral-tree">
                            <?php foreach ($referral_tree as $level => $referrals): ?>
                                <div class="level-section">
                                    <h6 class="level-title">
                                        <i class="fas fa-layer-group"></i> 
                                        Уровень <?php echo $level; ?> 
                                        <span class="badge bg-primary"><?php echo count($referrals); ?> чел.</span>
                                        <span class="badge bg-success"><?php echo $referral_rates[$level - 1] ?? 0; ?>%</span>
                                    </h6>
                                    
                                    <div class="referrals-grid">
                                        <?php foreach ($referrals as $referral): ?>
                                            <div class="referral-card">
                                                <div class="referral-avatar">
                                                    <img src="<?php echo $referral['avatar'] ?: 'assets/images/default-avatar.png'; ?>" 
                                                         alt="Avatar">
                                                </div>
                                                <div class="referral-info">
                                                    <div class="referral-name">
                                                        <?php echo htmlspecialchars($referral['first_name'] . ' ' . substr($referral['last_name'], 0, 1) . '.'); ?>
                                                    </div>
                                                    <div class="referral-date">
                                                        Регистрация: <?php echo date('d.m.Y', strtotime($referral['created_at'])); ?>
                                                    </div>
                                                    <div class="referral-stats">
                                                        <small class="text-muted">
                                                            Инвестировано: <?php echo formatMoney($referral['total_invested']); ?>
                                                        </small>
                                                    </div>
                                                    <div class="referral-earnings">
                                                        <span class="text-success">
                                                            Ваш доход: <?php echo formatMoney($referral['your_earnings']); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="referral-status">
                                                    <span class="badge bg-<?php echo $referral['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo $referral['status'] === 'active' ? 'Активен' : 'Неактивен'; ?>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Боковая панель -->
        <div class="col-lg-4">
            <!-- Условия программы -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Условия программы
                    </h5>
                </div>
                <div class="card-body">
                    <div class="referral-conditions">
                        <div class="condition-item">
                            <div class="condition-level">1 уровень</div>
                            <div class="condition-rate"><?php echo $referral_rates[0] ?? 5; ?>%</div>
                            <div class="condition-desc">от инвестиций прямых рефералов</div>
                        </div>
                        
                        <div class="condition-item">
                            <div class="condition-level">2 уровень</div>
                            <div class="condition-rate"><?php echo $referral_rates[1] ?? 3; ?>%</div>
                            <div class="condition-desc">от инвестиций рефералов 2-го уровня</div>
                        </div>
                        
                        <div class="condition-item">
                            <div class="condition-level">3 уровень</div>
                            <div class="condition-rate"><?php echo $referral_rates[2] ?? 1; ?>%</div>
                            <div class="condition-desc">от инвестиций рефералов 3-го уровня</div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Дополнительные бонусы:</h6>
                        <ul class="bonus-list">
                            <li>Мгновенное начисление бонусов</li>
                            <li>Пожизненные выплаты</li>
                            <li>Бонус $5 за каждого нового реферала</li>
                            <li>Специальные акции для активных партнеров</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- История заработка -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> История заработка
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($referral_earnings)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-coins fa-2x mb-2"></i>
                            <p>Заработка пока нет</p>
                        </div>
                    <?php else: ?>
                        <div class="earnings-list">
                            <?php foreach ($referral_earnings as $earning): ?>
                                <div class="earning-item">
                                    <div class="earning-info">
                                        <div class="earning-amount">+<?php echo formatMoney($earning['amount']); ?></div>
                                        <div class="earning-desc"><?php echo htmlspecialchars($earning['description']); ?></div>
                                        <div class="earning-date"><?php echo date('d.m.Y H:i', strtotime($earning['created_at'])); ?></div>
                                    </div>
                                    <div class="earning-level">
                                        <span class="badge bg-primary">Ур. <?php echo $earning['level']; ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="index.php?page=transactions&filter=referral" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list"></i> Все транзакции
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.referral-link-section {
    text-align: center;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.social-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.level-section {
    margin-bottom: 2rem;
}

.level-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.referrals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.referral-card {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.referral-card:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.referral-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.referral-info {
    flex: 1;
}

.referral-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.referral-date {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.referral-stats {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.referral-earnings {
    font-size: 0.9rem;
    font-weight: 600;
}

.condition-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.condition-level {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 80px;
}

.condition-rate {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--success-color);
    min-width: 60px;
    text-align: center;
}

.condition-desc {
    font-size: 0.9rem;
    color: #6c757d;
    flex: 1;
    margin-left: 1rem;
}

.bonus-list {
    list-style: none;
    padding: 0;
}

.bonus-list li {
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

.bonus-list li:before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
    margin-right: 0.5rem;
}

.earnings-list {
    max-height: 300px;
    overflow-y: auto;
}

.earning-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.earning-item:last-child {
    border-bottom: none;
}

.earning-amount {
    font-weight: 600;
    color: var(--success-color);
}

.earning-desc {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0.25rem 0;
}

.earning-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

@media (max-width: 768px) {
    .referrals-grid {
        grid-template-columns: 1fr;
    }
    
    .referral-card {
        flex-direction: column;
        text-align: center;
    }
    
    .condition-item {
        flex-direction: column;
        text-align: center;
    }
    
    .condition-desc {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}
</style>

<script>
const referralLink = "<?php echo SITE_URL; ?>/index.php?page=register&ref=<?php echo $user['referral_code']; ?>";
const shareText = "Присоединяйтесь к GreenChain EcoFund - инвестиционной платформе для экологических проектов! Получите бонус при регистрации.";

function shareToTelegram() {
    const url = `https://t.me/share/url?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent(shareText)}`;
    window.open(url, '_blank');
}

function shareToTwitter() {
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(referralLink)}`;
    window.open(url, '_blank');
}

function shareToFacebook() {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`;
    window.open(url, '_blank');
}

function shareToWhatsApp() {
    const url = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + referralLink)}`;
    window.open(url, '_blank');
}

function shareByEmail() {
    const subject = encodeURIComponent('Приглашение в GreenChain EcoFund');
    const body = encodeURIComponent(shareText + '\n\n' + referralLink);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
}
</script>

<?php
/**
 * Получение статистики рефералов
 */
function getReferralStats($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT r.referred_id) as total_referrals,
            COUNT(DISTINCT CASE WHEN u.status = 'active' THEN r.referred_id END) as active_referrals,
            COALESCE(SUM(r.total_earned), 0) as total_earnings,
            COALESCE(SUM(CASE WHEN DATE(t.created_at) = CURDATE() THEN t.amount ELSE 0 END), 0) as today_earnings
        FROM referrals r
        LEFT JOIN users u ON r.referred_id = u.id
        LEFT JOIN transactions t ON r.referrer_id = t.user_id AND t.type = 'referral_bonus'
        WHERE r.referrer_id = ? AND r.is_active = 1
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetch();
}

/**
 * Получение дерева рефералов
 */
function getReferralTree($user_id) {
    global $conn;
    
    $tree = [];
    
    // Получаем рефералов по уровням (до 3 уровня)
    for ($level = 1; $level <= 3; $level++) {
        $stmt = $conn->prepare("
            SELECT 
                u.id,
                u.first_name,
                u.last_name,
                u.avatar,
                u.status,
                u.created_at,
                u.total_invested,
                COALESCE(r.total_earned, 0) as your_earnings
            FROM referrals r
            JOIN users u ON r.referred_id = u.id
            WHERE r.referrer_id = ? AND r.level = ? AND r.is_active = 1
            ORDER BY u.created_at DESC
        ");
        $stmt->execute([$user_id, $level]);
        $referrals = $stmt->fetchAll();
        
        if (!empty($referrals)) {
            $tree[$level] = $referrals;
        }
    }
    
    return $tree;
}

/**
 * Получение истории реферальных заработков
 */
function getReferralEarnings($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            t.amount,
            t.description,
            t.created_at,
            CASE 
                WHEN t.description LIKE '%1 уровня%' THEN 1
                WHEN t.description LIKE '%2 уровня%' THEN 2
                WHEN t.description LIKE '%3 уровня%' THEN 3
                ELSE 1
            END as level
        FROM transactions t
        WHERE t.user_id = ? AND t.type = 'referral_bonus'
        ORDER BY t.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetchAll();
}
?>
