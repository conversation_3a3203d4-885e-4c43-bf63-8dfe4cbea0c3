/* ===== ДОПОЛНИТЕЛЬНЫЕ КОМПОНЕНТЫ GREENCHAIN ECOFUND ===== */

/* ===== КНОПКА "НАВЕРХ" ===== */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    display: none;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    z-index: var(--z-40);
    cursor: pointer;
}

.back-to-top:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow: var(--shadow-2xl);
    color: var(--white);
}

/* ===== ЖИВОЙ ЧАТ ===== */
.live-chat {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    z-index: var(--z-40);
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-success);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    cursor: pointer;
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-2xl);
}

.chat-window {
    position: absolute;
    bottom: 80px;
    left: 0;
    width: 350px;
    height: 450px;
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    display: none;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.chat-window.show {
    display: flex;
    animation: slideInUp 0.3s ease-out;
}

.chat-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-header h6 {
    margin: 0;
    font-weight: var(--font-semibold);
}

.chat-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: var(--text-lg);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.chat-message {
    max-width: 80%;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-xl);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
}

.chat-message.user {
    background: var(--primary-100);
    color: var(--primary-800);
    align-self: flex-end;
    border-bottom-right-radius: var(--radius-md);
}

.chat-message.bot {
    background: var(--gray-100);
    color: var(--gray-800);
    align-self: flex-start;
    border-bottom-left-radius: var(--radius-md);
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 0.5rem;
}

.chat-input input {
    flex: 1;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-xl);
    padding: 0.75rem 1rem;
    font-size: var(--text-sm);
    outline: none;
}

.chat-input input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(22, 132, 74, 0.1);
}

.chat-send {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.chat-send:hover {
    transform: scale(1.1);
}

/* ===== УВЕДОМЛЕНИЯ ===== */
.notification-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: var(--z-50);
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 400px;
}

.notification {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    border-left: 4px solid var(--primary-500);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    transform: translateX(100%);
    opacity: 0;
    transition: var(--transition);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: var(--success-500);
}

.notification.warning {
    border-left-color: var(--warning-500);
}

.notification.error {
    border-left-color: var(--danger-500);
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--text-sm);
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.notification.success .notification-icon {
    background: var(--success-500);
}

.notification.warning .notification-icon {
    background: var(--warning-500);
}

.notification.error .notification-icon {
    background: var(--danger-500);
}

.notification.info .notification-icon {
    background: var(--primary-500);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: 0.25rem;
    font-size: var(--text-sm);
}

.notification-message {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* ===== ПРОГРЕСС БАРЫ ===== */
.progress-modern {
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar-modern {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

.progress-percentage {
    color: var(--primary-600);
    font-weight: var(--font-semibold);
}

/* ===== СТАТИСТИЧЕСКИЕ КАРТОЧКИ ===== */
.stats-card-modern {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.stats-card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-card-modern:hover::before {
    transform: scaleX(1);
}

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stats-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--text-xl);
    box-shadow: var(--shadow-primary);
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
}

.stats-trend.up {
    background: var(--success-100);
    color: var(--success-700);
}

.stats-trend.down {
    background: var(--danger-100);
    color: var(--danger-700);
}

.stats-value {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stats-label {
    color: var(--gray-600);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

/* ===== МОБИЛЬНАЯ АДАПТАЦИЯ ===== */
@media (max-width: 767.98px) {
    .chat-window {
        width: 300px;
        height: 400px;
    }
    
    .notification-container {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
    
    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 44px;
        height: 44px;
    }
    
    .live-chat {
        bottom: 1rem;
        left: 1rem;
    }
    
    .chat-toggle {
        width: 50px;
        height: 50px;
        font-size: var(--text-lg);
    }
    
    .stats-card-modern {
        padding: 1rem;
    }
    
    .stats-value {
        font-size: var(--text-2xl);
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: var(--text-lg);
    }
}
