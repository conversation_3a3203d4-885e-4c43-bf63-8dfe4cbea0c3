/* ===== РОСКОШНЫЕ ИКОНКИ И ВИЗУАЛЬНЫЕ ЭЛЕМЕНТЫ ===== */

/* ===== КАСТОМНЫЕ ИКОНКИ ===== */
.luxury-icon-eco {
    background: linear-gradient(135deg, #16844a 0%, #22a05e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: bold;
}

.luxury-icon-investment {
    background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: bold;
}

.luxury-icon-premium {
    background: linear-gradient(135deg, #1a3d2e 0%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: bold;
}

/* ===== ДЕКОРАТИВНЫЕ ЭЛЕМЕНТЫ ===== */
.luxury-divider {
    height: 2px;
    background: linear-gradient(to right, transparent, var(--luxury-gold), transparent);
    margin: 2rem 0;
    position: relative;
}

.luxury-divider::before {
    content: '◆';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--luxury-cream);
    color: var(--luxury-gold);
    padding: 0 1rem;
    font-size: 1rem;
}

.luxury-corner-ornament {
    position: relative;
}

.luxury-corner-ornament::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    width: 20px;
    height: 20px;
    border-top: 2px solid var(--luxury-gold);
    border-left: 2px solid var(--luxury-gold);
}

.luxury-corner-ornament::after {
    content: '';
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-bottom: 2px solid var(--luxury-gold);
    border-right: 2px solid var(--luxury-gold);
}

/* ===== ФОНОВЫЕ ПАТТЕРНЫ ===== */
.luxury-pattern-leaves {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20 30 Q30 20 40 30 Q30 40 20 30" fill="%2316844a" opacity="0.05"/><path d="M60 70 Q70 60 80 70 Q70 80 60 70" fill="%2316844a" opacity="0.05"/><path d="M10 80 Q20 70 30 80 Q20 90 10 80" fill="%2316844a" opacity="0.03"/><path d="M70 20 Q80 10 90 20 Q80 30 70 20" fill="%2316844a" opacity="0.03"/></svg>');
    background-size: 100px 100px;
    background-repeat: repeat;
}

.luxury-pattern-geometric {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><polygon points="30,5 50,25 30,45 10,25" fill="%23d4af37" opacity="0.03"/><circle cx="30" cy="30" r="3" fill="%23d4af37" opacity="0.05"/></svg>');
    background-size: 60px 60px;
    background-repeat: repeat;
}

.luxury-pattern-dots {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><circle cx="20" cy="20" r="2" fill="%23d4af37" opacity="0.1"/><circle cx="10" cy="10" r="1" fill="%23d4af37" opacity="0.05"/><circle cx="30" cy="10" r="1" fill="%23d4af37" opacity="0.05"/><circle cx="10" cy="30" r="1" fill="%23d4af37" opacity="0.05"/><circle cx="30" cy="30" r="1" fill="%23d4af37" opacity="0.05"/></svg>');
    background-size: 40px 40px;
    background-repeat: repeat;
}

/* ===== СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ===== */
.luxury-text-shadow {
    text-shadow: 2px 2px 4px rgba(212, 175, 55, 0.3);
}

.luxury-box-shadow-gold {
    box-shadow: 
        0 4px 8px rgba(212, 175, 55, 0.1),
        0 8px 16px rgba(212, 175, 55, 0.1),
        0 16px 32px rgba(212, 175, 55, 0.1);
}

.luxury-border-gradient {
    position: relative;
    background: var(--luxury-cream);
    border-radius: 12px;
}

.luxury-border-gradient::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--luxury-gold), var(--luxury-dark-green), var(--luxury-gold));
    border-radius: 14px;
    z-index: -1;
}

/* ===== АНИМИРОВАННЫЕ ЭЛЕМЕНТЫ ===== */
.luxury-pulse {
    animation: luxuryPulse 2s infinite;
}

@keyframes luxuryPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
    }
}

.luxury-float {
    animation: luxuryFloat 3s ease-in-out infinite;
}

@keyframes luxuryFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.luxury-rotate {
    animation: luxuryRotate 20s linear infinite;
}

@keyframes luxuryRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* ===== ГРАДИЕНТНЫЕ ТЕКСТЫ ===== */
.luxury-text-gradient-primary {
    background: linear-gradient(135deg, var(--luxury-dark-green) 0%, var(--luxury-gold) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.luxury-text-gradient-gold {
    background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-bright-gold) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.luxury-text-gradient-green {
    background: linear-gradient(135deg, var(--luxury-dark-green) 0%, var(--luxury-medium-green) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== СПЕЦИАЛЬНЫЕ КНОПКИ ===== */
.luxury-btn-floating {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-luxury-button);
    border: none;
    color: var(--luxury-dark-green);
    font-size: 1.5rem;
    box-shadow: var(--shadow-luxury-lg);
    transition: var(--transition-luxury);
    z-index: 1000;
}

.luxury-btn-floating:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-luxury-xl);
}

.luxury-btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.3);
    color: var(--luxury-gold);
    transition: var(--transition-luxury);
}

.luxury-btn-glass:hover {
    background: rgba(212, 175, 55, 0.1);
    border-color: var(--luxury-gold);
    color: var(--luxury-gold);
}

/* ===== КАРТОЧКИ С ЭФФЕКТАМИ ===== */
.luxury-card-flip {
    perspective: 1000px;
    height: 300px;
}

.luxury-card-flip-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.luxury-card-flip:hover .luxury-card-flip-inner {
    transform: rotateY(180deg);
}

.luxury-card-flip-front,
.luxury-card-flip-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.luxury-card-flip-front {
    background: var(--gradient-luxury-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.luxury-card-flip-back {
    background: var(--gradient-luxury-hero);
    color: var(--luxury-cream);
    transform: rotateY(180deg);
}

/* ===== ПРОГРЕСС ИНДИКАТОРЫ ===== */
.luxury-progress-circle {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--luxury-gold) 0deg, var(--luxury-gold) var(--progress, 0deg), rgba(212, 175, 55, 0.2) var(--progress, 0deg));
    display: flex;
    align-items: center;
    justify-content: center;
}

.luxury-progress-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--luxury-cream);
}

.luxury-progress-circle .progress-text {
    position: relative;
    z-index: 1;
    font-weight: 700;
    color: var(--luxury-dark-green);
}

/* ===== TOOLTIP СТИЛИ ===== */
.luxury-tooltip {
    position: relative;
    cursor: pointer;
}

.luxury-tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--luxury-dark-green);
    color: var(--luxury-cream);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-luxury-fast);
    z-index: 1000;
}

.luxury-tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--luxury-dark-green);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-luxury-fast);
}

.luxury-tooltip:hover::before,
.luxury-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ===== МЕДИА ЗАПРОСЫ ===== */
@media (max-width: 768px) {
    .luxury-btn-floating {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        bottom: 20px;
        right: 20px;
    }
    
    .luxury-card-flip {
        height: 250px;
    }
    
    .luxury-progress-circle {
        width: 100px;
        height: 100px;
    }
    
    .luxury-progress-circle::before {
        width: 70px;
        height: 70px;
    }
}
