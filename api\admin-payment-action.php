<?php
// API для управления платежными заявками администратором
// GreenChain EcoFund - Admin Payment Management API

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../includes/functions.php';

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Метод не разрешен']);
    exit;
}

// Проверка авторизации и прав администратора
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Необходима авторизация']);
    exit;
}

$user = getCurrentUser();
if ($user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Недостаточно прав доступа']);
    exit;
}

try {
    // Получение данных запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }

    $action = $input['action'] ?? '';
    $request_type = $input['request_type'] ?? ''; // 'payment' или 'withdrawal'
    $request_id = intval($input['request_id'] ?? 0);
    $admin_comment = trim($input['admin_comment'] ?? '');
    $transaction_hash = trim($input['transaction_hash'] ?? '');

    // Валидация входных данных
    if (!in_array($action, ['approve', 'reject'])) {
        echo json_encode(['success' => false, 'message' => 'Некорректное действие']);
        exit;
    }

    if (!in_array($request_type, ['payment', 'withdrawal'])) {
        echo json_encode(['success' => false, 'message' => 'Некорректный тип заявки']);
        exit;
    }

    if ($request_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Некорректный ID заявки']);
        exit;
    }

    $admin_id = $user['id'];

    if ($request_type === 'payment') {
        // Обработка заявки на пополнение
        $result = processPaymentRequest($request_id, $action, $admin_id, $admin_comment);
    } else {
        // Обработка заявки на вывод
        $result = processWithdrawalRequest($request_id, $action, $admin_id, $admin_comment, $transaction_hash);
    }

    echo json_encode($result);

} catch (Exception $e) {
    error_log("Admin Payment Action Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Внутренняя ошибка сервера']);
}

// Функция обработки заявки на пополнение
function processPaymentRequest($request_id, $action, $admin_id, $admin_comment) {
    global $conn;

    try {
        // Получение заявки
        $stmt = $conn->prepare("SELECT * FROM payment_requests WHERE id = ? AND status = 'pending'");
        $stmt->execute([$request_id]);
        $request = $stmt->fetch();

        if (!$request) {
            return ['success' => false, 'message' => 'Заявка не найдена или уже обработана'];
        }

        $new_status = ($action === 'approve') ? 'approved' : 'rejected';

        // Обновление статуса заявки
        $stmt = $conn->prepare("
            UPDATE payment_requests 
            SET status = ?, admin_id = ?, admin_comment = ?, processed_at = NOW(), updated_at = NOW()
            WHERE id = ?
        ");

        if ($stmt->execute([$new_status, $admin_id, $admin_comment, $request_id])) {
            
            if ($action === 'approve') {
                // Пополнение баланса пользователя
                $balance_updated = updateUserBalance(
                    $request['user_id'], 
                    $request['amount'], 
                    "Пополнение баланса #$request_id"
                );

                if ($balance_updated) {
                    // Обновление статуса транзакции
                    updateTransactionStatus($request['user_id'], 'deposit_request', $request_id, 'completed');
                    
                    return [
                        'success' => true,
                        'message' => 'Заявка одобрена, баланс пользователя пополнен',
                        'action' => 'approved',
                        'amount' => $request['amount']
                    ];
                } else {
                    // Откат статуса заявки при ошибке пополнения
                    $stmt = $conn->prepare("UPDATE payment_requests SET status = 'pending' WHERE id = ?");
                    $stmt->execute([$request_id]);
                    
                    return ['success' => false, 'message' => 'Ошибка пополнения баланса'];
                }
            } else {
                // Отклонение заявки
                updateTransactionStatus($request['user_id'], 'deposit_request', $request_id, 'failed');
                
                return [
                    'success' => true,
                    'message' => 'Заявка отклонена',
                    'action' => 'rejected'
                ];
            }
        } else {
            return ['success' => false, 'message' => 'Ошибка обновления заявки'];
        }

    } catch (Exception $e) {
        error_log("Process Payment Request Error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка обработки заявки'];
    }
}

// Функция обработки заявки на вывод
function processWithdrawalRequest($request_id, $action, $admin_id, $admin_comment, $transaction_hash = '') {
    global $conn;

    try {
        // Получение заявки
        $stmt = $conn->prepare("SELECT * FROM withdrawal_requests WHERE id = ? AND status = 'pending'");
        $stmt->execute([$request_id]);
        $request = $stmt->fetch();

        if (!$request) {
            return ['success' => false, 'message' => 'Заявка не найдена или уже обработана'];
        }

        if ($action === 'approve') {
            // Одобрение заявки на вывод
            $stmt = $conn->prepare("
                UPDATE withdrawal_requests 
                SET status = 'approved', admin_id = ?, admin_comment = ?, processed_at = NOW(), updated_at = NOW()
                WHERE id = ?
            ");

            if ($stmt->execute([$admin_id, $admin_comment, $request_id])) {
                // Списание средств с баланса пользователя
                $balance_updated = updateUserBalance(
                    $request['user_id'], 
                    -$request['amount'], 
                    "Вывод средств #$request_id"
                );

                if ($balance_updated) {
                    updateTransactionStatus($request['user_id'], 'withdrawal_request', $request_id, 'completed');
                    
                    return [
                        'success' => true,
                        'message' => 'Заявка одобрена, средства списаны с баланса',
                        'action' => 'approved',
                        'amount' => $request['amount']
                    ];
                } else {
                    // Откат статуса при ошибке
                    $stmt = $conn->prepare("UPDATE withdrawal_requests SET status = 'pending' WHERE id = ?");
                    $stmt->execute([$request_id]);
                    
                    return ['success' => false, 'message' => 'Ошибка списания средств'];
                }
            }
        } else {
            // Отклонение заявки на вывод
            $stmt = $conn->prepare("
                UPDATE withdrawal_requests 
                SET status = 'rejected', admin_id = ?, admin_comment = ?, processed_at = NOW(), updated_at = NOW()
                WHERE id = ?
            ");

            if ($stmt->execute([$admin_id, $admin_comment, $request_id])) {
                updateTransactionStatus($request['user_id'], 'withdrawal_request', $request_id, 'failed');
                
                return [
                    'success' => true,
                    'message' => 'Заявка отклонена',
                    'action' => 'rejected'
                ];
            }
        }

        return ['success' => false, 'message' => 'Ошибка обновления заявки'];

    } catch (Exception $e) {
        error_log("Process Withdrawal Request Error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка обработки заявки'];
    }
}

// Функция обновления баланса пользователя
function updateUserBalance($user_id, $amount, $description) {
    global $conn;

    try {
        // Получение текущего баланса
        $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();

        if (!$user) {
            return false;
        }

        $old_balance = floatval($user['balance']);
        $new_balance = $old_balance + $amount;

        // Проверка на отрицательный баланс при выводе
        if ($new_balance < 0) {
            return false;
        }

        // Обновление баланса
        $stmt = $conn->prepare("UPDATE users SET balance = ?, updated_at = NOW() WHERE id = ?");
        if ($stmt->execute([$new_balance, $user_id])) {
            // Логирование изменения баланса
            logBalanceChange($user_id, $amount, $old_balance, $new_balance, $description);
            return true;
        }

        return false;

    } catch (Exception $e) {
        error_log("Update User Balance Error: " . $e->getMessage());
        return false;
    }
}

// Функция логирования изменений баланса
function logBalanceChange($user_id, $amount, $old_balance, $new_balance, $description) {
    global $conn;

    try {
        $type = ($amount > 0) ? 'deposit' : 'withdrawal';
        
        $stmt = $conn->prepare("
            INSERT INTO transactions 
            (user_id, type, amount, balance_before, balance_after, description, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, 'completed', NOW())
        ");
        
        $stmt->execute([$user_id, $type, abs($amount), $old_balance, $new_balance, $description]);
    } catch (Exception $e) {
        error_log("Log Balance Change Error: " . $e->getMessage());
    }
}

// Функция обновления статуса транзакции
function updateTransactionStatus($user_id, $type, $reference_id, $status) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            UPDATE transactions 
            SET status = ?, updated_at = NOW() 
            WHERE user_id = ? AND reference_id = ? AND reference_type LIKE ?
        ");
        
        $reference_type = ($type === 'deposit_request') ? '%payment_request%' : '%withdrawal_request%';
        $stmt->execute([$status, $user_id, $reference_id, $reference_type]);
    } catch (Exception $e) {
        error_log("Update Transaction Status Error: " . $e->getMessage());
    }
}
?>
