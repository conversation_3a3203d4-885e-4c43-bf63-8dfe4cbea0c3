/* ===== GREENCHAIN ECOFUND - РОСКОШНЫЙ ЭКО-ДИЗАЙН ===== */

/* ===== ИМПОРТ ШРИФТОВ ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600;700&display=swap');

/* ===== ПРЕМИАЛЬНАЯ ЧЕРНО-СИНЕ-ЗЕЛЕНАЯ ПАЛИТРА ===== */
:root {
    /* Основные черные тона */
    --luxury-black: #000000;
    --luxury-charcoal: #1a1a1a;
    --luxury-dark-charcoal: #0d0d0d;
    --luxury-soft-black: #2d2d2d;

    /* Темно-синие оттенки */
    --luxury-navy: #1e3a8a;
    --luxury-deep-navy: #1a365d;
    --luxury-midnight-blue: #0f1419;
    --luxury-royal-blue: #1e40af;

    /* Темно-зеленые оттенки */
    --luxury-dark-green: #1a3d2e;
    --luxury-forest-green: #0f2419;
    --luxury-deep-green: #0a1a12;
    --luxury-emerald-dark: #064e3b;
    --luxury-medium-green: #2d5a3d;

    /* Золотые акценты (сохранены) */
    --luxury-gold: #d4af37;
    --luxury-bright-gold: #ffd700;
    --luxury-dark-gold: #b8941f;
    --luxury-pale-gold: #f4e4a6;
    --luxury-antique-gold: #b8860b;

    /* Премиальные нейтральные */
    --luxury-cream: #faf8f3;
    --luxury-off-white: #f8f6f0;
    --luxury-warm-white: #ffffff;

    /* ===== КОНТРАСТНЫЕ ЦВЕТА ДЛЯ WCAG СООТВЕТСТВИЯ ===== */
    /* Текст на темных фонах (WCAG AAA) */
    --text-on-dark: #ffffff;
    --text-on-dark-secondary: #e2e8f0;
    --text-on-dark-muted: #cbd5e1;

    /* Текст на светлых фонах (WCAG AAA) */
    --text-on-light: #1a202c;
    --text-on-light-secondary: #2d3748;
    --text-on-light-muted: #4a5568;

    /* Текст на золотых фонах */
    --text-on-gold: #1a3d2e;
    --text-on-gold-secondary: #2d5a3d;

    /* Адаптивные цвета текста */
    --adaptive-text-primary: var(--text-on-light);
    --adaptive-text-secondary: var(--text-on-light-secondary);
    --adaptive-text-muted: var(--text-on-light-muted);

    /* Контрастные кнопки */
    --btn-contrast-bg: #ffffff;
    --btn-contrast-text: #1a3d2e;
    --btn-contrast-border: #d4af37;
    
    /* Премиальные градиенты */
    --gradient-luxury-primary: linear-gradient(135deg, var(--luxury-black) 0%, var(--luxury-navy) 50%, var(--luxury-dark-green) 100%);
    --gradient-luxury-hero: linear-gradient(135deg, var(--luxury-charcoal) 0%, var(--luxury-deep-navy) 40%, var(--luxury-forest-green) 80%, var(--luxury-gold) 100%);
    --gradient-luxury-card: linear-gradient(145deg, var(--luxury-charcoal) 0%, var(--luxury-soft-black) 100%);
    --gradient-luxury-button: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-bright-gold) 100%);
    --gradient-luxury-overlay: linear-gradient(rgba(0, 0, 0, 0.9), rgba(26, 26, 26, 0.8));
    --gradient-luxury-dark: linear-gradient(135deg, var(--luxury-black) 0%, var(--luxury-deep-navy) 50%, var(--luxury-deep-green) 100%);
    --gradient-luxury-elegant: linear-gradient(145deg, var(--luxury-midnight-blue) 0%, var(--luxury-charcoal) 50%, var(--luxury-forest-green) 100%);
    
    /* Тени с золотистым оттенком */
    --shadow-luxury-sm: 0 2px 4px rgba(212, 175, 55, 0.1);
    --shadow-luxury-md: 0 4px 12px rgba(212, 175, 55, 0.15);
    --shadow-luxury-lg: 0 8px 25px rgba(212, 175, 55, 0.2);
    --shadow-luxury-xl: 0 15px 35px rgba(212, 175, 55, 0.25);
    --shadow-luxury-glow: 0 0 20px rgba(212, 175, 55, 0.3);
    
    /* Типографика */
    --font-luxury-serif: 'Playfair Display', 'Crimson Text', Georgia, serif;
    --font-luxury-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    
    /* Переходы */
    --transition-luxury: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-luxury-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-luxury-slow: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== БАЗОВЫЕ СТИЛИ ===== */
body {
    font-family: var(--font-luxury-sans);
    background: var(--gradient-luxury-dark);
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Фоновые текстуры */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(26, 61, 46, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, var(--luxury-cream) 0%, var(--luxury-off-white) 100%);
    z-index: -1;
    pointer-events: none;
}

/* ===== ТИПОГРАФИКА С АДАПТИВНОЙ КОНТРАСТНОСТЬЮ ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-luxury-serif);
    font-weight: 600;
    color: var(--adaptive-text-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
    transition: var(--contrast-transition);
}

h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--adaptive-text-primary);
    /* Убираем градиент для лучшей читаемости */
}

h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--adaptive-text-primary);
}

h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--adaptive-text-primary);
}

.luxury-subtitle {
    font-family: var(--font-luxury-sans);
    font-size: 1.25rem;
    color: var(--adaptive-text-secondary);
    font-weight: 400;
    margin-bottom: 2rem;
    transition: var(--contrast-transition);
}

/* Специальные стили для заголовков в темных секциях */
.dark-section h1,
.dark-section h2,
.dark-section h3,
.dark-section h4,
.dark-section h5,
.dark-section h6,
.luxury-hero h1,
.luxury-hero h2,
.luxury-hero h3,
.luxury-hero h4,
.luxury-hero h5,
.luxury-hero h6 {
    color: var(--text-on-dark) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Специальные стили для подзаголовков в темных секциях */
.dark-section .luxury-subtitle,
.luxury-hero .luxury-subtitle {
    color: var(--text-on-dark-secondary) !important;
}

/* ===== КНОПКИ С ВЫСОКОЙ КОНТРАСТНОСТЬЮ ===== */
.btn-luxury-primary {
    background: var(--gradient-luxury-button);
    border: 2px solid var(--luxury-gold);
    color: var(--text-on-gold) !important;
    font-weight: 700;
    padding: 12px 32px;
    border-radius: 8px;
    transition: var(--transition-luxury);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-luxury-md);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-luxury-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition-luxury);
}

.btn-luxury-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-luxury-lg);
    border-color: var(--luxury-bright-gold);
}

.btn-luxury-primary:hover::before {
    left: 100%;
}

.btn-luxury-secondary {
    background: transparent;
    border: 2px solid var(--luxury-gold);
    color: var(--luxury-gold) !important;
    font-weight: 700;
    padding: 12px 32px;
    border-radius: 8px;
    transition: var(--transition-luxury);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-luxury-secondary:hover {
    background: var(--luxury-gold);
    color: var(--text-on-gold) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-luxury-md);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Адаптивные кнопки для темных секций */
.dark-section .btn-luxury-secondary,
.luxury-hero .btn-luxury-secondary {
    border-color: var(--luxury-bright-gold);
    color: var(--luxury-bright-gold) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark-section .btn-luxury-secondary:hover,
.luxury-hero .btn-luxury-secondary:hover {
    background: var(--luxury-bright-gold);
    color: var(--text-on-gold) !important;
}

/* ===== КАРТОЧКИ С АДАПТИВНОЙ КОНТРАСТНОСТЬЮ ===== */
.luxury-card {
    background: var(--gradient-luxury-card);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-luxury-md);
    transition: var(--transition-luxury);
    position: relative;
    overflow: hidden;
}

.luxury-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-luxury-button);
}

.luxury-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-luxury-lg);
    border-color: rgba(212, 175, 55, 0.4);
}

/* Контрастный текст в карточках */
.luxury-card h3,
.luxury-card h4,
.luxury-card h5,
.luxury-card h6 {
    color: var(--text-on-light) !important;
    font-weight: 700;
}

.luxury-card p,
.luxury-card .card-text {
    color: var(--text-on-light-secondary) !important;
    line-height: 1.6;
}

.luxury-card .text-muted {
    color: var(--text-on-light-muted) !important;
}

/* ===== НАВИГАЦИЯ С УЛУЧШЕННОЙ КОНТРАСТНОСТЬЮ ===== */
.luxury-navbar {
    background: var(--gradient-luxury-hero);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    padding: 1rem 0;
    transition: var(--transition-luxury);
}

.luxury-navbar.scrolled {
    background: rgba(15, 36, 25, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.luxury-navbar .navbar-brand {
    font-family: var(--font-luxury-serif);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-on-dark) !important;
    text-decoration: none;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: var(--contrast-transition);
}

.luxury-navbar .nav-link {
    color: var(--text-on-dark) !important;
    font-weight: 500;
    transition: var(--transition-luxury-fast);
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.luxury-navbar .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--luxury-gold);
    transition: var(--transition-luxury-fast);
}

.luxury-navbar .nav-link:hover {
    color: var(--luxury-gold);
}

.luxury-navbar .nav-link:hover::after {
    width: 100%;
}

/* ===== HERO СЕКЦИЯ ===== */
.luxury-hero {
    background: var(--gradient-luxury-hero);
    color: var(--luxury-cream);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.luxury-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23d4af37" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23d4af37" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23d4af37" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="%23d4af37" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.luxury-hero h1 {
    color: var(--text-on-dark) !important;
    background: none;
    -webkit-text-fill-color: initial;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.luxury-hero .luxury-subtitle {
    color: var(--text-on-dark-secondary) !important;
    font-size: 1.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* ===== СТАТИСТИЧЕСКИЕ КАРТОЧКИ ===== */
.luxury-stat-card {
    background: var(--gradient-luxury-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: var(--transition-luxury);
    position: relative;
    overflow: hidden;
}

.luxury-stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-luxury);
}

.luxury-stat-card:hover::before {
    opacity: 1;
}

.luxury-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-luxury-lg);
}

.luxury-stat-number {
    font-family: var(--font-luxury-serif);
    font-size: 3rem;
    font-weight: 700;
    color: var(--luxury-gold);
    margin-bottom: 0.5rem;
}

.luxury-stat-label {
    font-size: 1rem;
    color: var(--luxury-earth-brown);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== ФОРМЫ ===== */
.luxury-form-control {
    background: var(--luxury-charcoal);
    border: 2px solid rgba(212, 175, 55, 0.4);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: var(--transition-luxury-fast);
    color: #ffffff;
}

.luxury-form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.luxury-form-control:focus {
    outline: none;
    border-color: var(--luxury-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
    background: var(--luxury-soft-black);
    color: #ffffff;
}

.luxury-form-label {
    font-weight: 600;
    color: var(--luxury-dark-green);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== МОДАЛЬНЫЕ ОКНА ===== */
.luxury-modal .modal-content {
    background: var(--gradient-luxury-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 16px;
    box-shadow: var(--shadow-luxury-xl);
    overflow: hidden;
}

.luxury-modal .modal-header {
    background: var(--gradient-luxury-hero);
    color: var(--luxury-cream);
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    padding: 1.5rem 2rem;
}

.luxury-modal .modal-title {
    font-family: var(--font-luxury-serif);
    font-weight: 600;
    color: var(--luxury-gold);
}

.luxury-modal .modal-body {
    padding: 2rem;
}

/* ===== ТАБЛИЦЫ ===== */
.luxury-table {
    background: var(--luxury-charcoal);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-luxury-md);
    border: 1px solid rgba(212, 175, 55, 0.4);
    color: #ffffff;
}

.luxury-table thead {
    background: var(--gradient-luxury-hero);
    color: var(--luxury-cream);
}

.luxury-table th {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem;
    border: none;
}

.luxury-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(212, 175, 55, 0.1);
    vertical-align: middle;
}

.luxury-table tbody tr:hover {
    background: rgba(212, 175, 55, 0.05);
}

/* ===== ПРОГРЕСС БАРЫ ===== */
.luxury-progress {
    height: 8px;
    background: rgba(212, 175, 55, 0.2);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.luxury-progress-bar {
    background: var(--gradient-luxury-button);
    height: 100%;
    border-radius: 4px;
    transition: var(--transition-luxury);
    position: relative;
}

.luxury-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== БЕЙДЖИ И МЕТКИ ===== */
.luxury-badge {
    background: var(--gradient-luxury-button);
    color: var(--luxury-dark-green);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-luxury-sm);
}

.luxury-badge-success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
}

.luxury-badge-warning {
    background: linear-gradient(135deg, #92400e 0%, #78350f 100%);
    color: white;
}

.luxury-badge-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

/* ===== ИКОНКИ ===== */
.luxury-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-luxury-button);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--luxury-dark-green);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-luxury-sm);
}

.luxury-icon-lg {
    width: 64px;
    height: 64px;
    font-size: 2rem;
}

/* ===== АЛЕРТЫ ===== */
.luxury-alert {
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.luxury-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--luxury-gold);
}

.luxury-alert-success {
    background: rgba(34, 197, 94, 0.1);
    color: #166534;
    border-color: rgba(34, 197, 94, 0.3);
}

.luxury-alert-success::before {
    background: #22c55e;
}

.luxury-alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-color: rgba(245, 158, 11, 0.3);
}

.luxury-alert-warning::before {
    background: #92400e;
}

.luxury-alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-color: rgba(239, 68, 68, 0.3);
}

.luxury-alert-danger::before {
    background: #ef4444;
}

/* ===== ИНВЕСТИЦИОННЫЕ КАРТОЧКИ ===== */
.luxury-investment-card {
    background: var(--gradient-luxury-card);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    padding: 2.5rem;
    transition: var(--transition-luxury);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.luxury-investment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-luxury-button);
}

.luxury-investment-card::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition-luxury);
}

.luxury-investment-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-luxury-xl);
    border-color: var(--luxury-gold);
}

.luxury-investment-card:hover::after {
    opacity: 1;
    transform: scale(1.5);
}

.luxury-investment-title {
    font-family: var(--font-luxury-serif);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--luxury-dark-green);
    margin-bottom: 1rem;
}

.luxury-investment-rate {
    font-family: var(--font-luxury-serif);
    font-size: 3rem;
    font-weight: 700;
    color: var(--luxury-gold);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.luxury-investment-period {
    color: var(--luxury-earth-brown);
    font-size: 1rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== КАРТА ПРОЕКТОВ ===== */
.luxury-map-container {
    background: var(--gradient-luxury-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-luxury-lg);
    position: relative;
}

.luxury-map-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* ===== ПРОФИЛЬ ПОЛЬЗОВАТЕЛЯ ===== */
.luxury-profile-card {
    background: var(--gradient-luxury-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.luxury-profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid var(--luxury-gold);
    margin: 0 auto 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-luxury-md);
}

.luxury-profile-avatar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.2) 50%, transparent 70%);
    opacity: 0;
    transition: var(--transition-luxury);
}

.luxury-profile-avatar:hover::after {
    opacity: 1;
}

/* ===== СТАТИСТИКИ БАЛАНСА ===== */
.luxury-balance-card {
    background: var(--gradient-luxury-hero);
    color: var(--luxury-cream);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.luxury-balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.luxury-balance-amount {
    font-family: var(--font-luxury-serif);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--luxury-gold);
    margin-bottom: 0.5rem;
}

.luxury-balance-label {
    color: var(--luxury-pale-gold);
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .luxury-hero {
        padding: 4rem 0;
    }

    .luxury-card,
    .luxury-investment-card {
        padding: 1.5rem;
    }

    .luxury-investment-rate {
        font-size: 2.5rem;
    }

    .luxury-balance-amount {
        font-size: 2rem;
    }

    .btn-luxury-primary,
    .btn-luxury-secondary {
        padding: 10px 24px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2rem;
    }

    .luxury-hero {
        padding: 3rem 0;
    }

    .luxury-card,
    .luxury-investment-card {
        padding: 1rem;
    }

    .luxury-investment-rate {
        font-size: 2rem;
    }

    .luxury-balance-amount {
        font-size: 1.8rem;
    }
}

/* ===== СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ===== */
.luxury-glow {
    position: relative;
}

.luxury-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-luxury-button);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: var(--transition-luxury);
}

.luxury-glow:hover::before {
    opacity: 0.7;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
}

/* ===== ПЕРЕОПРЕДЕЛЕНИЕ BOOTSTRAP КЛАССОВ ===== */
.btn-success {
    background: var(--gradient-luxury-button) !important;
    border-color: var(--luxury-gold) !important;
    color: var(--luxury-dark-green) !important;
    font-weight: 600;
    transition: var(--transition-luxury);
}

.btn-success:hover {
    background: var(--luxury-bright-gold) !important;
    border-color: var(--luxury-bright-gold) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-luxury-md);
}

.btn-primary {
    background: var(--gradient-luxury-primary) !important;
    border-color: var(--luxury-dark-green) !important;
    color: var(--luxury-cream) !important;
    font-weight: 600;
    transition: var(--transition-luxury);
}

.btn-primary:hover {
    background: var(--luxury-navy) !important;
    border-color: var(--luxury-navy) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-luxury-md);
}

.btn-secondary {
    background: transparent !important;
    border-color: var(--luxury-earth-brown) !important;
    color: var(--luxury-earth-brown) !important;
    font-weight: 600;
    transition: var(--transition-luxury);
}

.btn-secondary:hover {
    background: var(--luxury-earth-brown) !important;
    border-color: var(--luxury-earth-brown) !important;
    color: var(--luxury-cream) !important;
    transform: translateY(-2px);
}

.card {
    background: var(--gradient-luxury-card) !important;
    border: 1px solid rgba(212, 175, 55, 0.2) !important;
    border-radius: 16px !important;
    box-shadow: var(--shadow-luxury-md) !important;
    transition: var(--transition-luxury);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-luxury-lg) !important;
}

.card-header {
    background: var(--gradient-luxury-hero) !important;
    color: var(--luxury-cream) !important;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3) !important;
    font-weight: 600;
}

.form-control {
    background: var(--luxury-charcoal) !important;
    border: 2px solid rgba(212, 175, 55, 0.4) !important;
    border-radius: 8px !important;
    transition: var(--transition-luxury-fast);
    color: #ffffff !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

.form-control:focus {
    border-color: var(--luxury-gold) !important;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3) !important;
    background: var(--luxury-soft-black) !important;
    color: #ffffff !important;
}

.form-label {
    font-weight: 600 !important;
    color: var(--luxury-dark-green) !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.table {
    background: var(--luxury-charcoal) !important;
    border-radius: 12px !important;
    overflow: hidden;
    box-shadow: var(--shadow-luxury-md) !important;
    color: #ffffff !important;
}

.table th,
.table td {
    color: #ffffff !important;
    border-color: rgba(212, 175, 55, 0.3) !important;
}

.table thead th {
    background: var(--gradient-luxury-hero) !important;
    color: var(--luxury-cream) !important;
    border: none !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background: rgba(212, 175, 55, 0.05) !important;
}

.modal-content {
    background: var(--gradient-luxury-card) !important;
    border: 1px solid rgba(212, 175, 55, 0.3) !important;
    border-radius: 16px !important;
    box-shadow: var(--shadow-luxury-xl) !important;
}

.modal-header {
    background: var(--gradient-luxury-hero) !important;
    color: var(--luxury-cream) !important;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.modal-title {
    font-family: var(--font-luxury-serif) !important;
    font-weight: 600 !important;
    color: var(--luxury-gold) !important;
}

/* ===== АНИМАЦИИ ЗАГРУЗКИ ===== */
.luxury-loading {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    border-top-color: var(--luxury-gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.luxury-loading-text {
    color: var(--luxury-earth-brown);
    font-style: italic;
    margin-left: 10px;
}
