/* <PERSON><PERSON><PERSON><PERSON> - Responsive Styles */

/* Extra Large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .stats-card {
        padding: var(--spacing-2xl);
    }
    
    .card-body {
        padding: var(--spacing-2xl);
    }
    
    .hero-section {
        padding: 100px 0;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .hero-title {
        font-size: 3.5rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .investment-packages {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .navbar-nav {
        margin-top: var(--spacing-md);
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .investment-packages {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .card-body {
        padding: var(--spacing-lg);
    }
    
    .footer-stats {
        justify-content: center;
        margin-top: var(--spacing-md);
    }
    
    .live-chat-widget {
        bottom: 80px;
        right: 20px;
    }
    
    .chat-window {
        width: 280px;
        height: 350px;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container {
        max-width: 540px;
    }
    
    .navbar-brand .brand-text {
        font-size: var(--font-size-lg);
    }
    
    .hero-section {
        padding: 60px 0;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .stats-card {
        padding: var(--spacing-lg);
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .stats-value {
        font-size: var(--font-size-2xl);
    }
    
    .investment-packages {
        grid-template-columns: 1fr;
    }
    
    .package-card {
        margin-bottom: var(--spacing-lg);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .table-responsive {
        font-size: var(--font-size-sm);
    }
    
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
    
    .modal-dialog {
        margin: var(--spacing-md);
    }
    
    .footer {
        text-align: center;
    }
    
    .footer-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    /* Header adjustments */
    .navbar {
        padding: var(--spacing-sm) 0;
    }
    
    .navbar-brand {
        font-size: var(--font-size-base);
    }
    
    .navbar-brand .logo {
        height: 30px;
    }
    
    .navbar-brand .brand-text {
        display: none;
    }
    
    .navbar-toggler {
        padding: var(--spacing-xs);
        font-size: var(--font-size-sm);
    }
    
    .navbar-nav .nav-link {
        padding: var(--spacing-sm) var(--spacing-md) !important;
        margin: var(--spacing-xs) 0;
        text-align: center;
    }
    
    .balance-badge {
        display: block;
        margin: var(--spacing-xs) 0 0 0;
        text-align: center;
    }
    
    .dropdown-menu {
        position: static !important;
        transform: none !important;
        width: 100%;
        box-shadow: none;
        border: 1px solid #e9ecef;
        margin-top: 0;
    }
    
    /* Main content */
    .main-content {
        padding: var(--spacing-lg) 0;
    }
    
    .hero-section {
        padding: 40px 0;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
        line-height: 1.3;
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-lg);
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .hero-buttons .btn {
        width: 100%;
    }
    
    /* Cards and components */
    .card {
        margin-bottom: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
    }
    
    .card-header {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .stats-card {
        padding: var(--spacing-md);
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }
    
    .stats-value {
        font-size: var(--font-size-xl);
    }
    
    .stats-label {
        font-size: var(--font-size-xs);
    }
    
    /* Investment packages */
    .investment-packages {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .package-card {
        padding: var(--spacing-md);
    }
    
    .package-price {
        font-size: var(--font-size-xl);
    }
    
    .package-features {
        font-size: var(--font-size-sm);
    }
    
    /* Dashboard */
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .dashboard-actions {
        width: 100%;
        justify-content: center;
    }
    
    /* Tables */
    .table-responsive {
        font-size: var(--font-size-xs);
        border-radius: var(--border-radius-md);
    }
    
    .table thead th {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .table tbody td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    /* Forms */
    .form-control {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
    
    .form-label {
        font-size: var(--font-size-sm);
    }
    
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius-lg) !important;
        margin-bottom: var(--spacing-xs);
    }
    
    /* Modals */
    .modal-dialog {
        margin: var(--spacing-sm);
        max-width: calc(100vw - 2rem);
    }
    
    .modal-header {
        padding: var(--spacing-md);
    }
    
    .modal-body {
        padding: var(--spacing-md);
    }
    
    .modal-footer {
        padding: var(--spacing-md);
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin: var(--spacing-xs) 0;
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-xl) 0 var(--spacing-md);
        text-align: center;
    }
    
    .footer-widget {
        margin-bottom: var(--spacing-lg);
    }
    
    .widget-title {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-md);
    }
    
    .widget-text {
        font-size: var(--font-size-sm);
    }
    
    .footer-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-md);
    }
    
    .footer-links li {
        margin: 0;
    }
    
    .footer-links a {
        font-size: var(--font-size-sm);
    }
    
    .social-links {
        justify-content: center;
        margin-top: var(--spacing-md);
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-sm);
    }
    
    .footer-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
        margin-top: var(--spacing-md);
    }
    
    .stat-item {
        font-size: var(--font-size-xs);
    }
    
    .copyright {
        font-size: var(--font-size-xs);
        text-align: center;
    }
    
    /* Live chat */
    .live-chat-widget {
        bottom: 60px;
        right: 15px;
    }
    
    .chat-toggle {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .chat-window {
        width: calc(100vw - 30px);
        height: 300px;
        right: -15px;
        bottom: 70px;
    }
    
    .chat-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .chat-header h6 {
        font-size: var(--font-size-sm);
        margin: 0;
    }
    
    .chat-messages {
        padding: var(--spacing-sm);
    }
    
    .message {
        font-size: var(--font-size-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .chat-input {
        padding: var(--spacing-sm);
    }
    
    .chat-input input {
        font-size: var(--font-size-sm);
        padding: var(--spacing-xs);
    }
    
    .chat-input button {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    /* Back to top */
    .btn-back-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
        font-size: var(--font-size-base);
    }
    
    /* Map adjustments */
    .map-container {
        height: 300px;
        border-radius: var(--border-radius-lg);
    }
    
    /* Calculator adjustments */
    .calculator-inputs {
        grid-template-columns: 1fr;
    }
    
    .calculator-result {
        padding: var(--spacing-md);
    }
    
    /* Investment form */
    .investment-form {
        padding: var(--spacing-md);
    }
    
    .amount-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
    
    .amount-buttons .btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
}

/* Landscape orientation adjustments */
@media (max-width: 767.98px) and (orientation: landscape) {
    .hero-section {
        padding: 30px 0;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle {
        font-size: 0.9rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chat-window {
        height: 250px;
    }
    
    .modal-dialog {
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .btn-back-to-top,
    .live-chat-widget,
    .navbar,
    .modal {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .text-gradient {
        color: var(--primary-color) !important;
        -webkit-text-fill-color: var(--primary-color) !important;
    }
    
    .bg-gradient {
        background: var(--primary-color) !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000;
        --secondary-color: #000;
        --light-color: #fff;
        --dark-color: #000;
    }
    
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .animate-fade-in,
    .animate-slide-in-right,
    .animate-pulse {
        animation: none;
    }
}
