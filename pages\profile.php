<?php
$page_title = "Профиль";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();

// Обработка форм
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            $result = updateProfile();
            break;
        case 'change_password':
            $result = changePassword();
            break;
        case 'upload_avatar':
            $result = uploadAvatar();
            break;
        default:
            $result = ['success' => false, 'message' => 'Неизвестное действие'];
    }
    
    if ($result['success']) {
        redirect('index.php?page=profile', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-user-cog text-primary"></i> Профиль
                </h2>
                <p class="page-subtitle">Управление личными данными и настройками</p>
            </div>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Основная информация -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> Основная информация
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Имя *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($user['first_name']); ?>" 
                                       required minlength="2" maxlength="50">
                                <div class="invalid-feedback">
                                    Введите ваше имя (минимум 2 символа)
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Фамилия *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($user['last_name']); ?>" 
                                       required minlength="2" maxlength="50">
                                <div class="invalid-feedback">
                                    Введите вашу фамилию (минимум 2 символа)
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" 
                                       required readonly>
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> Для изменения email обратитесь в поддержку
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Телефон</label>
                                <input type="tel" class="form-control phone-input" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($user['phone']); ?>" 
                                       placeholder="+7 (999) 123-45-67">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Страна</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="">Выберите страну</option>
                                    <option value="RU" <?php echo $user['country'] === 'RU' ? 'selected' : ''; ?>>Россия</option>
                                    <option value="UA" <?php echo $user['country'] === 'UA' ? 'selected' : ''; ?>>Украина</option>
                                    <option value="BY" <?php echo $user['country'] === 'BY' ? 'selected' : ''; ?>>Беларусь</option>
                                    <option value="KZ" <?php echo $user['country'] === 'KZ' ? 'selected' : ''; ?>>Казахстан</option>
                                    <option value="US" <?php echo $user['country'] === 'US' ? 'selected' : ''; ?>>США</option>
                                    <option value="DE" <?php echo $user['country'] === 'DE' ? 'selected' : ''; ?>>Германия</option>
                                    <option value="OTHER" <?php echo $user['country'] === 'OTHER' ? 'selected' : ''; ?>>Другая</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">Город</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="<?php echo htmlspecialchars($user['city']); ?>" 
                                       maxlength="100">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Адрес</label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      maxlength="500"><?php echo htmlspecialchars($user['address']); ?></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Сохранить изменения
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Смена пароля -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lock"></i> Смена пароля
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Текущий пароль *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="current_password" 
                                       name="current_password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Введите текущий пароль
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Новый пароль *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" 
                                       name="new_password" required minlength="8">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Минимум 8 символов, должен содержать заглавные и строчные буквы, цифры
                            </div>
                            <div class="invalid-feedback">
                                Пароль должен содержать минимум 8 символов
                            </div>
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" id="passwordStrength" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="passwordStrengthText"></small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Подтверждение пароля *</label>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" required>
                            <div class="invalid-feedback">
                                Пароли не совпадают
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key"></i> Изменить пароль
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Боковая панель -->
        <div class="col-lg-4">
            <!-- Аватар -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-image"></i> Фото профиля
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="avatar-container mb-3">
                        <img src="<?php echo $user['avatar'] ?: 'assets/images/default-avatar.svg'; ?>"
                             alt="Аватар" class="avatar-image" id="avatarPreview">
                        <div class="avatar-overlay">
                            <i class="fas fa-camera"></i>
                        </div>
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data" id="avatarForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="upload_avatar">
                        <input type="file" class="d-none" id="avatarInput" name="avatar" 
                               accept="image/*" onchange="previewAvatar(this)">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('avatarInput').click()">
                            <i class="fas fa-upload"></i> Загрузить фото
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm d-none" id="saveAvatarBtn">
                            <i class="fas fa-save"></i> Сохранить
                        </button>
                    </form>
                    
                    <div class="form-text mt-2">
                        Максимальный размер: 2MB<br>
                        Форматы: JPG, PNG, GIF
                    </div>
                </div>
            </div>
            
            <!-- Статистика профиля -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> Статистика
                    </h5>
                </div>
                <div class="card-body">
                    <div class="profile-stat">
                        <span class="stat-label">Дата регистрации:</span>
                        <span class="stat-value"><?php echo date('d.m.Y', strtotime($user['created_at'])); ?></span>
                    </div>
                    <div class="profile-stat">
                        <span class="stat-label">Последний вход:</span>
                        <span class="stat-value">
                            <?php echo $user['last_login'] ? date('d.m.Y H:i', strtotime($user['last_login'])) : 'Первый вход'; ?>
                        </span>
                    </div>
                    <div class="profile-stat">
                        <span class="stat-label">Статус аккаунта:</span>
                        <span class="stat-value">
                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?>">
                                <?php echo $user['status'] === 'active' ? 'Активен' : 'Неактивен'; ?>
                            </span>
                        </span>
                    </div>
                    <div class="profile-stat">
                        <span class="stat-label">Email подтвержден:</span>
                        <span class="stat-value">
                            <?php if ($user['email_verified']): ?>
                                <span class="text-success"><i class="fas fa-check-circle"></i> Да</span>
                            <?php else: ?>
                                <span class="text-warning"><i class="fas fa-exclamation-circle"></i> Нет</span>
                                <a href="index.php?page=resend-verification" class="btn btn-sm btn-outline-warning mt-1">
                                    Подтвердить
                                </a>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- Реферальная ссылка -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link"></i> Реферальная ссылка
                    </h5>
                </div>
                <div class="card-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="referralLink" 
                               value="<?php echo SITE_URL; ?>/index.php?page=register&ref=<?php echo $user['referral_code']; ?>" 
                               readonly>
                        <button class="btn btn-outline-primary copy-btn" type="button" 
                                data-copy="<?php echo SITE_URL; ?>/index.php?page=register&ref=<?php echo $user['referral_code']; ?>">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    
                    <div class="referral-stats">
                        <?php
                        $referral_stats = getReferralStats($user['id']);
                        ?>
                        <div class="stat-item">
                            <span class="stat-label">Приглашено:</span>
                            <span class="stat-value"><?php echo $referral_stats['total_referrals']; ?> чел.</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Заработано:</span>
                            <span class="stat-value"><?php echo formatMoney($referral_stats['total_earnings']); ?></span>
                        </div>
                    </div>
                    
                    <a href="index.php?page=referrals" class="btn btn-primary btn-sm w-100 mt-2">
                        <i class="fas fa-users"></i> Подробнее о рефералах
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-container {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
    cursor: pointer;
}

.avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e9ecef;
    transition: all 0.3s ease;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.profile-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.profile-stat:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 600;
}

.referral-stats .stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-strength .progress-bar.weak {
    background-color: #dc3545;
}

.password-strength .progress-bar.medium {
    background-color: #ffc107;
}

.password-strength .progress-bar.strong {
    background-color: #28a745;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Проверка силы пароля
    document.getElementById('new_password').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');
        
        let strength = 0;
        let text = '';
        
        if (password.length >= 8) strength += 25;
        if (/[a-z]/.test(password)) strength += 25;
        if (/[A-Z]/.test(password)) strength += 25;
        if (/[0-9]/.test(password)) strength += 25;
        
        strengthBar.style.width = strength + '%';
        
        if (strength < 50) {
            strengthBar.className = 'progress-bar weak';
            text = 'Слабый пароль';
        } else if (strength < 100) {
            strengthBar.className = 'progress-bar medium';
            text = 'Средний пароль';
        } else {
            strengthBar.className = 'progress-bar strong';
            text = 'Сильный пароль';
        }
        
        strengthText.textContent = text;
    });
    
    // Проверка совпадения паролей
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('new_password').value;
        const confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('Пароли не совпадают');
        } else {
            this.setCustomValidity('');
        }
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            document.getElementById('avatarPreview').src = e.target.result;
            document.getElementById('saveAvatarBtn').classList.remove('d-none');
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}
</script>

<?php
/**
 * Обновление профиля
 */
function updateProfile() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $phone = sanitizeInput($_POST['phone']);
    $country = sanitizeInput($_POST['country']);
    $city = sanitizeInput($_POST['city']);
    $address = sanitizeInput($_POST['address']);
    $user_id = $_SESSION['user_id'];
    
    // Валидация
    if (empty($first_name) || strlen($first_name) < 2) {
        return ['success' => false, 'message' => 'Имя должно содержать минимум 2 символа'];
    }
    
    if (empty($last_name) || strlen($last_name) < 2) {
        return ['success' => false, 'message' => 'Фамилия должна содержать минимум 2 символа'];
    }
    
    try {
        $stmt = $conn->prepare("
            UPDATE users 
            SET first_name = ?, last_name = ?, phone = ?, country = ?, city = ?, address = ?
            WHERE id = ?
        ");
        $stmt->execute([$first_name, $last_name, $phone, $country, $city, $address, $user_id]);
        
        // Логирование
        logAction('profile_updated', "User ID: $user_id");
        
        return ['success' => true, 'message' => 'Профиль успешно обновлен!'];
        
    } catch (Exception $e) {
        error_log("Profile update error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обновлении профиля'];
    }
}

/**
 * Смена пароля
 */
function changePassword() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    $user_id = $_SESSION['user_id'];
    
    // Валидация
    if (!validatePassword($new_password)) {
        return ['success' => false, 'message' => 'Новый пароль не соответствует требованиям безопасности'];
    }
    
    if ($new_password !== $confirm_password) {
        return ['success' => false, 'message' => 'Пароли не совпадают'];
    }
    
    try {
        // Проверка текущего пароля
        $stmt = $conn->prepare("SELECT password_hash FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!verifyPassword($current_password, $user['password_hash'])) {
            return ['success' => false, 'message' => 'Неверный текущий пароль'];
        }
        
        // Обновление пароля
        $new_password_hash = hashPassword($new_password);
        $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
        $stmt->execute([$new_password_hash, $user_id]);
        
        // Логирование
        logAction('password_changed', "User ID: $user_id");
        
        return ['success' => true, 'message' => 'Пароль успешно изменен!'];
        
    } catch (Exception $e) {
        error_log("Password change error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при изменении пароля'];
    }
}

/**
 * Загрузка аватара
 */
function uploadAvatar() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Ошибка при загрузке файла'];
    }
    
    $file = $_FILES['avatar'];
    $user_id = $_SESSION['user_id'];
    
    // Валидация файла
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'message' => 'Недопустимый тип файла. Разрешены: JPG, PNG, GIF'];
    }
    
    if ($file['size'] > 2 * 1024 * 1024) { // 2MB
        return ['success' => false, 'message' => 'Размер файла не должен превышать 2MB'];
    }
    
    try {
        // Создание директории для аватаров
        $upload_dir = 'uploads/avatars/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // Генерация имени файла
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'avatar_' . $user_id . '_' . time() . '.' . $extension;
        $filepath = $upload_dir . $filename;
        
        // Перемещение файла
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => false, 'message' => 'Ошибка при сохранении файла'];
        }
        
        // Обновление пути к аватару в БД
        $stmt = $conn->prepare("UPDATE users SET avatar = ? WHERE id = ?");
        $stmt->execute([$filepath, $user_id]);
        
        // Логирование
        logAction('avatar_uploaded', "User ID: $user_id, File: $filename");
        
        return ['success' => true, 'message' => 'Аватар успешно загружен!'];
        
    } catch (Exception $e) {
        error_log("Avatar upload error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при загрузке аватара'];
    }
}

/**
 * Получение статистики рефералов
 */
function getReferralStats($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_referrals,
            COALESCE(SUM(total_earned), 0) as total_earnings
        FROM referrals 
        WHERE referrer_id = ? AND is_active = 1
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetch();
}
?>
