<?php
$page_title = "Карта проектов";

// Получение проектов для карты
$projects = getMapProjects();
$project_stats = getProjectStats();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-globe-americas text-success"></i> Карта экологических проектов
                </h2>
                <p class="page-subtitle">Интерактивная карта наших проектов по всему миру</p>
            </div>
        </div>
    </div>
    
    <!-- Статистика проектов -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $project_stats['total_projects']; ?></div>
                    <div class="stats-label">Всего проектов</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $project_stats['active_projects']; ?></div>
                    <div class="stats-label">Активных проектов</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($project_stats['total_investment']); ?></div>
                    <div class="stats-label">Общие инвестиции</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $project_stats['countries_count']; ?></div>
                    <div class="stats-label">Стран</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Карта -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marked-alt"></i> Интерактивная карта
                    </h5>
                    <div class="map-controls">
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="mapFilter" id="filterAll" value="all" checked>
                            <label class="btn btn-outline-primary" for="filterAll">Все</label>
                            
                            <input type="radio" class="btn-check" name="mapFilter" id="filterSolar" value="solar_farm">
                            <label class="btn btn-outline-primary" for="filterSolar">Солнечные</label>
                            
                            <input type="radio" class="btn-check" name="mapFilter" id="filterWind" value="wind_farm">
                            <label class="btn btn-outline-primary" for="filterWind">Ветряные</label>
                            
                            <input type="radio" class="btn-check" name="mapFilter" id="filterMining" value="mining_farm">
                            <label class="btn btn-outline-primary" for="filterMining">Майнинг</label>
                            
                            <input type="radio" class="btn-check" name="mapFilter" id="filterEco" value="eco_project">
                            <label class="btn btn-outline-primary" for="filterEco">Эко-проекты</label>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="worldMap" class="map-container"></div>
                </div>
            </div>
        </div>
        
        <!-- Список проектов -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Проекты
                    </h5>
                </div>
                <div class="card-body">
                    <div class="projects-list" id="projectsList">
                        <?php foreach ($projects as $project): ?>
                            <div class="project-item" data-project-id="<?php echo $project['id']; ?>" 
                                 data-project-type="<?php echo $project['type']; ?>"
                                 data-lat="<?php echo $project['latitude']; ?>" 
                                 data-lng="<?php echo $project['longitude']; ?>">
                                <div class="project-header">
                                    <div class="project-icon">
                                        <i class="<?php echo getProjectIcon($project['type']); ?>"></i>
                                    </div>
                                    <div class="project-info">
                                        <h6 class="project-name"><?php echo htmlspecialchars($project['name']); ?></h6>
                                        <p class="project-location">
                                            <i class="fas fa-map-marker-alt"></i> 
                                            <?php echo htmlspecialchars($project['city'] . ', ' . $project['country']); ?>
                                        </p>
                                    </div>
                                    <div class="project-status">
                                        <span class="badge bg-<?php echo getStatusColor($project['status']); ?>">
                                            <?php echo getStatusName($project['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="project-details">
                                    <div class="detail-row">
                                        <span class="label">Тип:</span>
                                        <span class="value"><?php echo getProjectTypeName($project['type']); ?></span>
                                    </div>
                                    <?php if ($project['capacity']): ?>
                                        <div class="detail-row">
                                            <span class="label">Мощность:</span>
                                            <span class="value"><?php echo htmlspecialchars($project['capacity']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($project['investment_amount']): ?>
                                        <div class="detail-row">
                                            <span class="label">Инвестиции:</span>
                                            <span class="value"><?php echo formatMoney($project['investment_amount']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($project['roi_percentage']): ?>
                                        <div class="detail-row">
                                            <span class="label">ROI:</span>
                                            <span class="value text-success"><?php echo formatPercent($project['roi_percentage']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="project-actions">
                                    <button class="btn btn-sm btn-outline-primary" onclick="focusOnProject(<?php echo $project['latitude']; ?>, <?php echo $project['longitude']; ?>)">
                                        <i class="fas fa-search-plus"></i> На карте
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="showProjectDetails(<?php echo $project['id']; ?>)">
                                        <i class="fas fa-info-circle"></i> Подробнее
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно деталей проекта -->
<div class="modal fade" id="projectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-leaf"></i> Детали проекта
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="projectModalBody">
                <!-- Содержимое загружается динамически -->
            </div>
        </div>
    </div>
</div>

<style>
.map-container {
    height: 500px;
    width: 100%;
    border-radius: 0 0 0.5rem 0.5rem;
}

.stats-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.projects-list {
    max-height: 500px;
    overflow-y: auto;
}

.project-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.project-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.project-item.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(46, 139, 87, 0.2);
}

.project-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.project-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.project-info {
    flex: 1;
}

.project-name {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.project-location {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 0;
}

.project-status {
    flex-shrink: 0;
}

.project-details {
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

.detail-row .label {
    color: #6c757d;
}

.detail-row .value {
    font-weight: 600;
}

.project-actions {
    display: flex;
    gap: 0.5rem;
}

.map-controls {
    display: flex;
    gap: 0.5rem;
}

/* Стили для маркеров карты */
.custom-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.custom-marker:hover {
    transform: scale(1.2);
}

.marker-solar { background: #ffc107; }
.marker-wind { background: #17a2b8; }
.marker-mining { background: #6f42c1; }
.marker-eco { background: #28a745; }

/* Popup стили */
.project-popup {
    max-width: 250px;
}

.popup-header {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.popup-details {
    font-size: 0.85rem;
    color: #6c757d;
}

.popup-status {
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .map-container {
        height: 400px;
    }
    
    .map-controls {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group {
        width: 100%;
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    .project-actions {
        flex-direction: column;
    }
    
    .project-header {
        flex-direction: column;
        text-align: center;
    }
    
    .project-icon {
        margin: 0 auto 0.5rem;
    }
}
</style>

<!-- Подключение Leaflet для карты -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let markers = [];
let projectsData = <?php echo json_encode($projects); ?>;

document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    initializeFilters();
});

function initializeMap() {
    // Создание карты
    map = L.map('worldMap').setView([55.7558, 37.6176], 2);
    
    // Добавление тайлов
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Добавление маркеров проектов
    addProjectMarkers();
}

function addProjectMarkers() {
    projectsData.forEach(project => {
        const marker = createProjectMarker(project);
        markers.push({
            marker: marker,
            project: project
        });
    });
}

function createProjectMarker(project) {
    // Создание кастомной иконки
    const iconClass = getMarkerClass(project.type);
    const icon = L.divIcon({
        className: 'custom-marker ' + iconClass,
        html: getProjectIconHtml(project.type),
        iconSize: [30, 30],
        iconAnchor: [15, 15],
        popupAnchor: [0, -15]
    });
    
    // Создание маркера
    const marker = L.marker([project.latitude, project.longitude], { icon: icon })
        .addTo(map)
        .bindPopup(createPopupContent(project));
    
    // Обработчик клика
    marker.on('click', function() {
        highlightProject(project.id);
        showProjectDetails(project.id);
    });
    
    return marker;
}

function createPopupContent(project) {
    return `
        <div class="project-popup">
            <div class="popup-header">${project.name}</div>
            <div class="popup-details">
                <div><i class="fas fa-map-marker-alt"></i> ${project.city}, ${project.country}</div>
                <div><i class="fas fa-cog"></i> ${getProjectTypeName(project.type)}</div>
                ${project.capacity ? `<div><i class="fas fa-bolt"></i> ${project.capacity}</div>` : ''}
                ${project.investment_amount ? `<div><i class="fas fa-dollar-sign"></i> ${formatMoney(project.investment_amount)}</div>` : ''}
            </div>
            <div class="popup-status">
                <span class="badge bg-${getStatusColor(project.status)}">${getStatusName(project.status)}</span>
            </div>
        </div>
    `;
}

function getMarkerClass(type) {
    const classes = {
        'solar_farm': 'marker-solar',
        'wind_farm': 'marker-wind',
        'mining_farm': 'marker-mining',
        'eco_project': 'marker-eco'
    };
    return classes[type] || 'marker-eco';
}

function getProjectIconHtml(type) {
    const icons = {
        'solar_farm': '<i class="fas fa-sun"></i>',
        'wind_farm': '<i class="fas fa-wind"></i>',
        'mining_farm': '<i class="fas fa-microchip"></i>',
        'eco_project': '<i class="fas fa-leaf"></i>'
    };
    return icons[type] || '<i class="fas fa-leaf"></i>';
}

function initializeFilters() {
    document.querySelectorAll('input[name="mapFilter"]').forEach(radio => {
        radio.addEventListener('change', function() {
            filterProjects(this.value);
        });
    });
}

function filterProjects(filterType) {
    markers.forEach(item => {
        const shouldShow = filterType === 'all' || item.project.type === filterType;
        
        if (shouldShow) {
            item.marker.addTo(map);
        } else {
            map.removeLayer(item.marker);
        }
    });
    
    // Фильтрация списка проектов
    document.querySelectorAll('.project-item').forEach(item => {
        const projectType = item.dataset.projectType;
        const shouldShow = filterType === 'all' || projectType === filterType;
        item.style.display = shouldShow ? 'block' : 'none';
    });
}

function focusOnProject(lat, lng) {
    map.setView([lat, lng], 8);
}

function highlightProject(projectId) {
    // Убираем выделение со всех проектов
    document.querySelectorAll('.project-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Выделяем выбранный проект
    const projectItem = document.querySelector(`[data-project-id="${projectId}"]`);
    if (projectItem) {
        projectItem.classList.add('active');
        projectItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

function showProjectDetails(projectId) {
    console.log('Loading project details for ID:', projectId);

    // Показываем индикатор загрузки
    const modalBody = document.getElementById('projectModalBody');
    modalBody.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x"></i><br><br>Загрузка...</div>';

    fetch(`api/project-details.php?id=${projectId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Project details loaded:', data);
            if (data.success) {
                modalBody.innerHTML = createProjectDetailsHTML(data.project);
                new bootstrap.Modal(document.getElementById('projectModal')).show();
            } else {
                modalBody.innerHTML = '<div class="alert alert-danger">Ошибка: ' + (data.message || 'Не удалось загрузить данные') + '</div>';
                new bootstrap.Modal(document.getElementById('projectModal')).show();
            }
        })
        .catch(error => {
            console.error('Error loading project details:', error);
            modalBody.innerHTML = '<div class="alert alert-danger">Ошибка загрузки данных проекта</div>';
            new bootstrap.Modal(document.getElementById('projectModal')).show();
        });
}

function createProjectDetailsHTML(project) {
    return `
        <div class="project-details-modal">
            <div class="row">
                <div class="col-md-6">
                    <h6>Основная информация</h6>
                    <table class="table table-sm">
                        <tr><td>Название:</td><td>${project.name}</td></tr>
                        <tr><td>Тип:</td><td>${getProjectTypeName(project.type)}</td></tr>
                        <tr><td>Статус:</td><td><span class="badge bg-${getStatusColor(project.status)}">${getStatusName(project.status)}</span></td></tr>
                        <tr><td>Местоположение:</td><td>${project.city}, ${project.country}</td></tr>
                        ${project.capacity ? `<tr><td>Мощность:</td><td>${project.capacity}</td></tr>` : ''}
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Финансовые показатели</h6>
                    <table class="table table-sm">
                        ${project.investment_amount ? `<tr><td>Инвестиции:</td><td>${formatMoney(project.investment_amount)}</td></tr>` : ''}
                        ${project.roi_percentage ? `<tr><td>ROI:</td><td class="text-success">${formatPercent(project.roi_percentage)}</td></tr>` : ''}
                        ${project.start_date ? `<tr><td>Дата запуска:</td><td>${formatDate(project.start_date)}</td></tr>` : ''}
                        ${project.completion_date ? `<tr><td>Завершение:</td><td>${formatDate(project.completion_date)}</td></tr>` : ''}
                    </table>
                </div>
            </div>
            ${project.description ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Описание</h6>
                        <p>${project.description}</p>
                    </div>
                </div>
            ` : ''}
        </div>
    `;
}

// JavaScript версии PHP функций
function getProjectTypeName(type) {
    const names = {
        'solar_farm': 'Солнечная ферма',
        'wind_farm': 'Ветропарк',
        'mining_farm': 'Майнинг ферма',
        'eco_project': 'Эко-проект',
        'hydro': 'Гидроэлектростанция'
    };
    return names[type] || 'Неизвестный тип';
}

function getStatusColor(status) {
    const colors = {
        'active': 'success',
        'completed': 'primary',
        'pending': 'warning',
        'cancelled': 'danger',
        'paused': 'secondary',
        'planning': 'info',
        'construction': 'warning',
        'maintenance': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getStatusName(status) {
    const names = {
        'active': 'Активный',
        'completed': 'Завершен',
        'pending': 'Ожидание',
        'cancelled': 'Отменен',
        'paused': 'Приостановлен',
        'planning': 'Планирование',
        'construction': 'Строительство',
        'maintenance': 'Обслуживание'
    };
    return names[status] || 'Неизвестно';
}

function formatMoney(amount) {
    return '$' + parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatPercent(percent) {
    return parseFloat(percent).toFixed(2) + '%';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ru-RU');
}

// Обновление маркеров карты (для real-time обновлений)
function updateMapMarkers(projectStats) {
    // Эта функция будет вызываться из realtime.js
    console.log('Updating map markers with new stats:', projectStats);
}

// Вспомогательные функции
function getProjectTypeName(type) {
    const names = {
        'solar_farm': 'Солнечная ферма',
        'wind_farm': 'Ветряная ферма',
        'mining_farm': 'Майнинг-ферма',
        'eco_project': 'Эко-проект'
    };
    return names[type] || type;
}

function getStatusColor(status) {
    const colors = {
        'planning': 'secondary',
        'construction': 'warning',
        'active': 'success',
        'maintenance': 'info',
        'completed': 'primary'
    };
    return colors[status] || 'secondary';
}

function getStatusName(status) {
    const names = {
        'planning': 'Планирование',
        'construction': 'Строительство',
        'active': 'Активен',
        'maintenance': 'Обслуживание',
        'completed': 'Завершен'
    };
    return names[status] || status;
}

function formatMoney(amount) {
    return '$' + parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });
}

function formatPercent(percent) {
    return parseFloat(percent).toFixed(1) + '%';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ru-RU');
}
</script>


