/* ===== GREENCHAIN ECOFUND - ПРЕМИУМ ДИЗАЙН ПО СКРИНШОТАМ ===== */

/* ===== ИМПОРТ ШРИФТОВ ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* ===== CSS ПЕРЕМЕННЫЕ ===== */
:root {
    /* Основные цвета из скриншотов */
    --primary-green: #16844a;
    --primary-blue: #2563eb;
    --dark-bg: #0f172a;
    --dark-card: #1e293b;
    --light-bg: #ffffff;
    --light-card: #f8fafc;
    
    /* Градиенты */
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-hero: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    
    /* Текст */
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --text-light: #ffffff;
    --text-muted: #94a3b8;
    
    /* Тени */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Радиусы */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Переходы */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===== БАЗОВЫЕ СТИЛИ ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-bg);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== ТИПОГРАФИКА ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h1 { font-size: 3.5rem; font-weight: 800; }
h2 { font-size: 2.5rem; font-weight: 700; }
h3 { font-size: 2rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* ===== НАВИГАЦИЯ ===== */
.navbar {
    background: var(--gradient-hero) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    transition: var(--transition-normal);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--text-light) !important;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.navbar-brand:hover {
    color: var(--primary-green) !important;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    font-size: 1.75rem;
    color: var(--primary-green);
}

.brand-text {
    font-weight: 800;
    font-size: 1.25rem;
    color: var(--text-light);
}

.brand-subtitle {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    margin-top: -0.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.75rem 1rem !important;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--text-light) !important;
    background-color: rgba(255, 255, 255, 0.1);
}

/* ===== HERO СЕКЦИЯ ===== */
.hero-section {
    background: var(--gradient-hero);
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge .badge {
    background: rgba(34, 197, 94, 0.2) !important;
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    font-weight: 400;
    max-width: 600px;
}

.hero-stats {
    margin-bottom: 2rem;
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.hero-stats .stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== HERO IMAGE ===== */
.hero-image {
    position: relative;
    z-index: 2;
}

/* УДАЛЕНО: Стили инвестиционных карточек */

/* ===== КНОПКИ ===== */
.btn {
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: var(--radius-lg);
    border: none;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-light);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--text-light);
}

.btn-outline-primary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: var(--text-light);
    transform: translateY(-2px);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.125rem;
}

/* ===== КАРТОЧКИ ===== */
.card {
    background: var(--gradient-card);
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    background: var(--gradient-primary);
    color: var(--text-light);
    border: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

.card-footer {
    background: transparent;
    border: none;
    padding: 1.5rem 2rem;
}

/* ===== СТАТИСТИКА ===== */
.stats-section {
    background: var(--light-card);
    padding: 80px 0;
}

.stats-card {
    background: var(--light-bg);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.stats-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--text-light);
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
    
    .stats-card {
        padding: 2rem 1.5rem;
    }
    
    .stats-value {
        font-size: 2rem;
    }
}

/* ===== ИНВЕСТИЦИОННЫЕ ПАКЕТЫ ===== */
.packages-section {
    padding: 80px 0;
    background: var(--light-bg);
}

.package-card {
    background: var(--light-bg);
    border: 2px solid rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.package-card.border-primary {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 1px var(--primary-blue), var(--shadow-xl);
}

.package-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.package-rate {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-light);
    margin-bottom: 0;
}

.package-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--shadow-md);
}

.package-features {
    margin: 1.5rem 0;
}

.feature {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.feature i {
    margin-right: 0.75rem;
    width: 20px;
    font-size: 1.125rem;
}

.package-example {
    background: var(--light-card);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.package-example h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
}

.example-calc div {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 500;
}

.example-calc div:last-child {
    border-bottom: none;
    font-weight: 700;
    color: var(--primary-green);
    font-size: 1.125rem;
}

/* ===== СЕКЦИИ ===== */
.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 3rem;
    font-weight: 400;
}

/* ===== FEATURES СЕКЦИЯ ===== */
.features-section {
    padding: 80px 0;
    background: var(--light-card);
}

.feature-card {
    background: var(--light-bg);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--text-light);
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-description {
    color: var(--text-secondary);
    font-weight: 400;
    line-height: 1.6;
}

@media (max-width: 576px) {
    .hero-section {
        padding: 80px 0 60px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .stats-section {
        padding: 60px 0;
    }

    .package-rate {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

/* ===== STEPS СЕКЦИЯ ===== */
.how-it-works-section {
    padding: 80px 0;
    background: var(--light-bg);
}

.step-card {
    background: var(--light-bg);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    height: 100%;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.step-number {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.25rem;
    box-shadow: var(--shadow-md);
}

.step-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-green), var(--accent-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.5rem auto 1.5rem;
    font-size: 2rem;
    color: var(--text-light);
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.step-description {
    color: var(--text-secondary);
    font-weight: 400;
    line-height: 1.6;
}

/* ===== CTA СЕКЦИЯ ===== */
.cta-section {
    padding: 80px 0;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2.5rem;
    font-weight: 400;
}

.btn-light {
    background: var(--light-bg);
    color: var(--text-primary);
    border: none;
}

.btn-light:hover {
    background: var(--light-card);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.btn-outline-light {
    background: transparent;
    color: var(--text-light);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: var(--text-light);
}

/* ===== ФУТЕР ===== */
.footer {
    background: var(--dark-bg);
    color: var(--text-light);
    padding: 60px 0 30px;
}

.footer-widget {
    margin-bottom: 2rem;
}

.widget-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.widget-text {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-weight: 400;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--text-light);
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-blue);
    color: var(--text-light);
    transform: translateY(-2px);
}

.contact-info p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.75rem;
    font-weight: 400;
}

.contact-info i {
    margin-right: 0.75rem;
    width: 20px;
    color: var(--primary-blue);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.copyright {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    margin: 0;
}

.footer-stats {
    display: flex;
    gap: 2rem;
    justify-content: flex-end;
}

.stat-item {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .footer-stats {
        justify-content: flex-start;
        margin-top: 1rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-subtitle {
        font-size: 1.125rem;
    }
}

/* ===== ПАРТНЕРЫ ===== */
.partners-section {
    background: var(--light-card);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.partners-logos {
    gap: 3rem !important;
}

.partner-logo {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-secondary);
    opacity: 0.7;
    transition: var(--transition-fast);
}

.partner-logo:hover {
    opacity: 1;
    color: var(--text-primary);
}

/* ===== FEATURED INVESTMENTS ===== */
.featured-investments-section {
    background: var(--light-bg);
    padding: 80px 0;
}

.section-badge .badge {
    background: rgba(34, 197, 94, 0.1) !important;
    color: var(--primary-green);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    border: 1px solid rgba(34, 197, 94, 0.2);
    margin-bottom: 1rem;
}

.investment-card {
    background: var(--light-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.investment-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.investment-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.investment-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.investment-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--primary-green);
    color: var(--text-light);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.investment-price {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: var(--text-light);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.investment-content {
    padding: 1.5rem;
}

.investment-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.investment-category {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.investment-category i {
    color: var(--primary-green);
}

.stat-small {
    text-align: center;
}

.stat-small .stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-small .stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .partners-logos {
        gap: 2rem !important;
    }

    .partner-logo {
        font-size: 1rem;
    }

    .investment-image {
        height: 180px;
    }

    .investment-content {
        padding: 1.25rem;
    }
}
