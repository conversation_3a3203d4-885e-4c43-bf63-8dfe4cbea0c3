<?php
$page_title = "Инвестирование";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();
$user_balance = $user['balance'];

// Получение доступных пакетов (временная заглушка)
$packages = [];

// Получение параметров из URL для автоматического открытия модального окна
$auto_open_package_id = isset($_GET['package_id']) ? intval($_GET['package_id']) : 0;
$auto_open_package_type = isset($_GET['package_type']) ? $_GET['package_type'] : '';

// УДАЛЕНО: Старая обработка формы инвестирования
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-chart-line text-primary"></i> Инвестирование
                </h2>
                <p class="page-subtitle">Выберите подходящий инвестиционный пакет</p>
            </div>
        </div>
    </div>
    
    <!-- Баланс пользователя -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="balance-card animate-fadeInUp">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title mb-1">
                                <i class="fas fa-wallet"></i> Доступный баланс
                            </h5>
                            <h3 class="mb-0 user-balance animate-glow"><?php echo formatMoney($user_balance); ?></h3>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <a href="index.php?page=deposit" class="action-btn">
                                <i class="fas fa-plus"></i> Пополнить баланс
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <!-- Инвестиционные пакеты -->
    <div class="row">
        <?php foreach ($packages as $package): ?>
            <div class="col-lg-6 mb-4">
                <div class="card package-card h-100 <?php echo $package['type'] === 'fixed' ? 'border-primary' : ''; ?>">
                    <?php if ($package['type'] === 'fixed'): ?>
                        <div class="package-badge">Рекомендуем</div>
                    <?php endif; ?>
                    
                    <div class="card-header <?php echo $package['type'] === 'fixed' ? 'bg-gradient' : ''; ?> text-center">
                        <h4 class="package-title <?php echo $package['type'] === 'fixed' ? 'text-white' : ''; ?>">
                            <?php echo htmlspecialchars($package['name']); ?>
                        </h4>
                        <div class="package-rate <?php echo $package['type'] === 'fixed' ? 'text-white' : ''; ?>">
                            <?php echo formatPercent($package['daily_rate']); ?> в день
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="package-info mb-3">
                            <div class="info-item">
                                <i class="fas fa-dollar-sign text-success"></i>
                                <span>Минимум: <?php echo formatMoney($package['min_amount']); ?></span>
                            </div>
                            <?php if ($package['max_amount']): ?>
                                <div class="info-item">
                                    <i class="fas fa-dollar-sign text-warning"></i>
                                    <span>Максимум: <?php echo formatMoney($package['max_amount']); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if ($package['duration_days']): ?>
                                <div class="info-item">
                                    <i class="fas fa-calendar text-info"></i>
                                    <span>Срок: <?php echo $package['duration_days']; ?> дней</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="package-features mb-3">
                            <?php 
                            $features = json_decode($package['features'], true);
                            if ($features):
                                foreach ($features as $feature):
                            ?>
                                <div class="feature">
                                    <i class="fas fa-check text-success"></i>
                                    <?php echo htmlspecialchars($feature); ?>
                                </div>
                            <?php 
                                endforeach;
                            endif; 
                            ?>
                        </div>
                        
                        <div class="package-example">
                            <h6>Пример доходности:</h6>
                            <div class="example-calc" data-rate="<?php echo $package['daily_rate']; ?>">
                                <div class="calc-row">
                                    <span>Инвестиция: $1,000</span>
                                    <span>$1,000</span>
                                </div>
                                <div class="calc-row">
                                    <span>Ежедневно:</span>
                                    <span class="daily-profit">$<?php echo number_format(1000 * ($package['daily_rate'] / 100), 2); ?></span>
                                </div>
                                <div class="calc-row">
                                    <span>В месяц:</span>
                                    <span class="monthly-profit">$<?php echo number_format(1000 * ($package['daily_rate'] / 100) * 30, 2); ?></span>
                                </div>
                                <div class="calc-row total">
                                    <span>В год:</span>
                                    <span class="yearly-profit">$<?php echo number_format(1000 * ($package['daily_rate'] / 100) * 365, 2); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer text-center">
                        <!-- УДАЛЕНО: Старая кнопка инвестирования -->
                        <button type="button" class="btn btn-secondary btn-lg w-100" disabled>
                            <i class="fas fa-tools"></i> Система в разработке
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Статистика инвестиций -->
    <div class="row mt-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-chart-bar text-primary"></i> Ваша статистика
            </h4>
        </div>
        
        <?php
        $user_stats = getUserInvestmentStats($user['id']);
        ?>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-value"><?php echo formatMoney($user_stats['total_invested']); ?></div>
                <div class="stats-label">Всего инвестировано</div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stats-value"><?php echo formatMoney($user_stats['total_profit']); ?></div>
                <div class="stats-label">Общая прибыль</div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="stats-value"><?php echo $user_stats['active_investments']; ?></div>
                <div class="stats-label">Активных инвестиций</div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stats-value"><?php echo formatPercent($user_stats['avg_daily_rate']); ?></div>
                <div class="stats-label">Средняя доходность</div>
            </div>
        </div>
    </div>
</div>

<!-- УДАЛЕНО: Старое модальное окно инвестирования -->

<style>
.package-card {
    position: relative;
    transition: all 0.3s ease;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.package-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: var(--warning-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1;
}

.package-rate {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.info-item, .feature {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.info-item i, .feature i {
    margin-right: 0.5rem;
    width: 16px;
}

.example-calc .calc-row {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.example-calc .calc-row.total {
    border-bottom: none;
    font-weight: 600;
    color: var(--success-color);
}

.amount-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.profit-calculator {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.calc-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.calc-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
}

.calc-item .value {
    font-weight: 600;
    color: var(--success-color);
}

.balance-info {
    text-align: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 0.5rem;
}

.balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.info-grid {
    display: grid;
    gap: 0.5rem;
}

.info-grid .info-item {
    display: flex;
    justify-content: space-between;
}

.info-grid .label {
    color: #6c757d;
}

.info-grid .value {
    font-weight: 600;
}

@media (max-width: 768px) {
    .amount-buttons {
        justify-content: center;
    }
    
    .calc-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- УДАЛЕНО: Старые JavaScript функции инвестирования -->

<?php
// УДАЛЕНО: Старая функция getInvestmentPackages

// УДАЛЕНО: Старая функция getUserInvestmentStats

// УДАЛЕНО: Старая функция processInvestment

// УДАЛЕНО: Старая функция processReferralBonus
?>

<!-- УДАЛЕНО: Старый скрипт автоматического открытия модального окна -->
