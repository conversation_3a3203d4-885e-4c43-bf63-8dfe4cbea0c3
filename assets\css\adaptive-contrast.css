/* ===== АДАПТИВНАЯ КОНТРАСТНОСТЬ ДЛЯ WCAG СООТВЕТСТВИЯ ===== */

/* ===== УТИЛИТЫ ДЛЯ ОПРЕДЕЛЕНИЯ КОНТРАСТНОСТИ ===== */
:root {
    /* Пороговые значения яркости */
    --brightness-threshold: 0.5;
    
    /* Контрастные цвета для темных секций */
    --dark-section-text: #ffffff;
    --dark-section-text-secondary: #e2e8f0;
    --dark-section-text-muted: #cbd5e1;
    
    /* Контрастные цвета для светлых секций */
    --light-section-text: #1a202c;
    --light-section-text-secondary: #2d3748;
    --light-section-text-muted: #4a5568;
    
    /* Переходы для плавной смены цветов */
    --contrast-transition: color 0.3s ease, background-color 0.3s ease;
}

/* ===== БАЗОВЫЕ КЛАССЫ ДЛЯ АДАПТИВНОЙ КОНТРАСТНОСТИ ===== */

/* Автоматическая адаптация текста на основе фона */
.adaptive-text {
    transition: var(--contrast-transition);
}

/* Темные секции - светлый текст */
.dark-section,
.luxury-hero,
.bg-luxury-hero,
.luxury-navbar,
.bg-dark,
[class*="bg-luxury-"][class*="dark"],
[style*="background"][style*="#1a3d2e"],
[style*="background"][style*="#0f2419"],
[style*="background"][style*="#2d5a3d"] {
    --adaptive-text-primary: var(--dark-section-text);
    --adaptive-text-secondary: var(--dark-section-text-secondary);
    --adaptive-text-muted: var(--dark-section-text-muted);
}

.dark-section *,
.luxury-hero *,
.bg-luxury-hero *,
.luxury-navbar *,
.bg-dark * {
    color: var(--adaptive-text-primary) !important;
}

.dark-section .text-secondary,
.luxury-hero .text-secondary,
.bg-luxury-hero .text-secondary,
.luxury-navbar .text-secondary,
.bg-dark .text-secondary {
    color: var(--adaptive-text-secondary) !important;
}

.dark-section .text-muted,
.luxury-hero .text-muted,
.bg-luxury-hero .text-muted,
.luxury-navbar .text-muted,
.bg-dark .text-muted {
    color: var(--adaptive-text-muted) !important;
}

/* Светлые секции преобразованы в темные для белого текста */
.light-section,
.bg-light,
.bg-white,
.luxury-card,
.bg-luxury-investment,
[class*="bg-luxury-"][class*="light"],
[class*="bg-luxury-"][class*="cream"],
[style*="background"][style*="#faf8f3"],
[style*="background"][style*="#f8f6f0"],
[style*="background"][style*="#ffffff"] {
    background: var(--luxury-dark-green) !important;
    --adaptive-text-primary: var(--dark-section-text);
    --adaptive-text-secondary: var(--dark-section-text-secondary);
    --adaptive-text-muted: var(--dark-section-text-muted);
}

.light-section *,
.bg-light *,
.bg-white *,
.luxury-card *,
.bg-luxury-investment * {
    color: #ffffff !important;
}

.light-section .text-secondary,
.bg-light .text-secondary,
.bg-white .text-secondary,
.luxury-card .text-secondary,
.bg-luxury-investment .text-secondary {
    color: var(--adaptive-text-secondary) !important;
}

.light-section .text-muted,
.bg-light .text-muted,
.bg-white .text-muted,
.luxury-card .text-muted,
.bg-luxury-investment .text-muted {
    color: var(--adaptive-text-muted) !important;
}

/* ===== СПЕЦИАЛЬНЫЕ КЛАССЫ ДЛЯ КОНТРАСТНОСТИ ===== */

/* Высококонтрастный текст */
.high-contrast-text {
    color: var(--adaptive-text-primary) !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Контрастные заголовки */
.contrast-heading {
    color: var(--adaptive-text-primary) !important;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Контрастные кнопки */
.btn-high-contrast {
    background: var(--btn-contrast-bg) !important;
    color: var(--btn-contrast-text) !important;
    border: 2px solid var(--btn-contrast-border) !important;
    font-weight: 600;
    transition: var(--contrast-transition);
}

.btn-high-contrast:hover {
    background: var(--btn-contrast-border) !important;
    color: var(--btn-contrast-bg) !important;
    transform: translateY(-2px);
}

/* ===== ПЕРЕОПРЕДЕЛЕНИЯ ДЛЯ ЛУЧШЕЙ КОНТРАСТНОСТИ ===== */

/* Заголовки в темных секциях */
.dark-section h1,
.dark-section h2,
.dark-section h3,
.dark-section h4,
.dark-section h5,
.dark-section h6,
.luxury-hero h1,
.luxury-hero h2,
.luxury-hero h3,
.luxury-hero h4,
.luxury-hero h5,
.luxury-hero h6,
.bg-luxury-hero h1,
.bg-luxury-hero h2,
.bg-luxury-hero h3,
.bg-luxury-hero h4,
.bg-luxury-hero h5,
.bg-luxury-hero h6 {
    color: var(--dark-section-text) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Подзаголовки в темных секциях */
.dark-section .luxury-subtitle,
.luxury-hero .luxury-subtitle,
.bg-luxury-hero .luxury-subtitle {
    color: var(--dark-section-text-secondary) !important;
}

/* Ссылки в темных секциях */
.dark-section a,
.luxury-hero a,
.bg-luxury-hero a,
.luxury-navbar a {
    color: var(--luxury-pale-gold) !important;
    transition: var(--contrast-transition);
}

.dark-section a:hover,
.luxury-hero a:hover,
.bg-luxury-hero a:hover,
.luxury-navbar a:hover {
    color: var(--luxury-bright-gold) !important;
    text-decoration: none;
}

/* Статистические карточки */
.luxury-stat-number {
    color: var(--luxury-gold) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.luxury-stat-label {
    color: var(--adaptive-text-secondary) !important;
}

/* Формы с высокой контрастностью */
.form-control.high-contrast {
    background: var(--luxury-dark-green) !important;
    color: #ffffff !important;
    border: 2px solid var(--luxury-gold) !important;
}

.form-control.high-contrast::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

.form-control.high-contrast:focus {
    border-color: var(--luxury-gold) !important;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.25) !important;
}

.form-label.high-contrast {
    color: var(--adaptive-text-primary) !important;
    font-weight: 700;
}

/* ===== АДАПТИВНЫЕ ИКОНКИ ===== */
.adaptive-icon {
    transition: var(--contrast-transition);
}

.dark-section .adaptive-icon,
.luxury-hero .adaptive-icon,
.bg-luxury-hero .adaptive-icon {
    color: var(--dark-section-text) !important;
}

.light-section .adaptive-icon,
.bg-light .adaptive-icon,
.bg-white .adaptive-icon {
    color: var(--light-section-text) !important;
}

/* ===== СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ДЛЯ КОНТРАСТНОСТИ ===== */

/* Контрастная тень для текста на сложных фонах */
.contrast-shadow {
    text-shadow: 
        0 1px 2px rgba(0, 0, 0, 0.8),
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Контрастный фон для критически важного текста */
.contrast-bg {
    background: rgba(255, 255, 255, 0.95) !important;
    color: var(--light-section-text) !important;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

/* Контрастная рамка */
.contrast-border {
    border: 2px solid var(--adaptive-text-primary) !important;
    border-radius: 8px;
}

/* ===== ACCESSIBILITY УЛУЧШЕНИЯ ===== */

/* Фокус с высокой контрастностью */
.high-contrast-focus:focus,
.btn:focus,
.form-control:focus,
a:focus {
    outline: 3px solid var(--luxury-bright-gold) !important;
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(255, 215, 0, 0.3) !important;
}

/* Улучшенная видимость для пользователей с нарушениями зрения */
@media (prefers-contrast: high) {
    :root {
        --adaptive-text-primary: #000000;
        --dark-section-text: #ffffff;
        --light-section-text: #000000;
    }
    
    .luxury-card,
    .btn,
    .form-control {
        border-width: 3px !important;
    }
    
    .luxury-hero,
    .bg-luxury-hero {
        background: #000000 !important;
    }
    
    .bg-light,
    .bg-white,
    .luxury-card {
        background: var(--luxury-dark-green) !important;
        color: #ffffff !important;
    }
}

/* Поддержка уменьшенной анимации */
@media (prefers-reduced-motion: reduce) {
    .adaptive-text,
    .btn-high-contrast,
    .adaptive-icon {
        transition: none !important;
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА ДЛЯ БЕЛОГО ТЕКСТА ===== */

/* Принудительный белый текст для всех элементов */
.force-white-text,
.force-white-text * {
    color: #ffffff !important;
}

/* Карточки с темным фоном */
.card,
.luxury-card {
    background: var(--luxury-dark-green) !important;
    color: #ffffff !important;
    border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.card *,
.luxury-card * {
    color: #ffffff !important;
}

/* Модальные окна */
.modal-content {
    background: var(--luxury-dark-green) !important;
    color: #ffffff !important;
}

.modal-header,
.modal-body,
.modal-footer {
    background: var(--luxury-dark-green) !important;
    color: #ffffff !important;
    border-color: rgba(212, 175, 55, 0.3) !important;
}

.modal-title,
.modal-header *,
.modal-body *,
.modal-footer * {
    color: #ffffff !important;
}

/* Дропдауны */
.dropdown-menu {
    background: var(--luxury-dark-green) !important;
    border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.dropdown-item {
    color: #ffffff !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: var(--luxury-medium-green) !important;
    color: #ffffff !important;
}

/* Навигация */
.nav-tabs .nav-link {
    background: var(--luxury-medium-green) !important;
    color: #ffffff !important;
    border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.nav-tabs .nav-link.active {
    background: var(--luxury-dark-green) !important;
    color: #ffffff !important;
    border-bottom-color: var(--luxury-dark-green) !important;
}

/* Пагинация */
.pagination .page-link {
    background: var(--luxury-medium-green) !important;
    color: #ffffff !important;
    border: 1px solid rgba(212, 175, 55, 0.3) !important;
}

.pagination .page-link:hover {
    background: var(--luxury-dark-green) !important;
    color: #ffffff !important;
}

.pagination .page-item.active .page-link {
    background: var(--luxury-gold) !important;
    color: var(--luxury-dark-green) !important;
    border-color: var(--luxury-gold) !important;
}

/* ===== МЕДИА ЗАПРОСЫ ДЛЯ МОБИЛЬНЫХ УСТРОЙСТВ ===== */
@media (max-width: 768px) {
    .contrast-shadow {
        text-shadow: 
            0 1px 2px rgba(0, 0, 0, 0.9),
            0 2px 4px rgba(0, 0, 0, 0.5);
    }
    
    .high-contrast-focus:focus,
    .btn:focus,
    .form-control:focus,
    a:focus {
        outline-width: 4px !important;
        outline-offset: 3px;
    }
    
    .contrast-bg {
        padding: 0.75rem 1.25rem;
        font-size: 1.1rem;
    }
}

/* ===== ЧЕРНЫЙ ТЕКСТ ДЛЯ КОЛОНОК ===== */

/* Принудительный черный цвет для любых колонок */
.black-text-column,
.black-text-column *,
.col-black-text,
.col-black-text * {
    color: #000000 !important;
}

/* Черный текст для таблиц */
.table-black-text,
.table-black-text *,
.table-black-text th,
.table-black-text td,
.table-black-text thead,
.table-black-text tbody,
.table-black-text tfoot {
    color: #000000 !important;
}

/* Черный текст для конкретных колонок таблиц */
.table .col-black,
.table .black-column,
table .col-black,
table .black-column {
    color: #000000 !important;
}

/* Черный текст для статистических карточек */
.stats-card.black-text,
.stats-card.black-text *,
.stats-card.black-text .stats-value,
.stats-card.black-text .stats-label,
.stats-card.black-text .stats-change {
    color: #000000 !important;
}

/* Черный текст для инвестиционных карточек */
.investment-card.black-text,
.investment-card.black-text *,
.investment-card.black-text .investment-title,
.investment-card.black-text .investment-amount,
.investment-card.black-text .investment-stats {
    color: #000000 !important;
}

/* Черный текст для карточек лидерборда */
.leaderboard-item.black-text,
.leaderboard-item.black-text *,
.leaderboard-item.black-text .leader-name,
.leaderboard-item.black-text .leader-stats {
    color: #000000 !important;
}

/* Черный текст для Bootstrap колонок */
.col-1.black-text, .col-1.black-text *,
.col-2.black-text, .col-2.black-text *,
.col-3.black-text, .col-3.black-text *,
.col-4.black-text, .col-4.black-text *,
.col-5.black-text, .col-5.black-text *,
.col-6.black-text, .col-6.black-text *,
.col-7.black-text, .col-7.black-text *,
.col-8.black-text, .col-8.black-text *,
.col-9.black-text, .col-9.black-text *,
.col-10.black-text, .col-10.black-text *,
.col-11.black-text, .col-11.black-text *,
.col-12.black-text, .col-12.black-text *,
.col-lg-1.black-text, .col-lg-1.black-text *,
.col-lg-2.black-text, .col-lg-2.black-text *,
.col-lg-3.black-text, .col-lg-3.black-text *,
.col-lg-4.black-text, .col-lg-4.black-text *,
.col-lg-5.black-text, .col-lg-5.black-text *,
.col-lg-6.black-text, .col-lg-6.black-text *,
.col-lg-7.black-text, .col-lg-7.black-text *,
.col-lg-8.black-text, .col-lg-8.black-text *,
.col-lg-9.black-text, .col-lg-9.black-text *,
.col-lg-10.black-text, .col-lg-10.black-text *,
.col-lg-11.black-text, .col-lg-11.black-text *,
.col-lg-12.black-text, .col-lg-12.black-text *,
.col-md-1.black-text, .col-md-1.black-text *,
.col-md-2.black-text, .col-md-2.black-text *,
.col-md-3.black-text, .col-md-3.black-text *,
.col-md-4.black-text, .col-md-4.black-text *,
.col-md-5.black-text, .col-md-5.black-text *,
.col-md-6.black-text, .col-md-6.black-text *,
.col-md-7.black-text, .col-md-7.black-text *,
.col-md-8.black-text, .col-md-8.black-text *,
.col-md-9.black-text, .col-md-9.black-text *,
.col-md-10.black-text, .col-md-10.black-text *,
.col-md-11.black-text, .col-md-11.black-text *,
.col-md-12.black-text, .col-md-12.black-text * {
    color: #000000 !important;
}

/* Черный текст для конкретных элементов */
.black-text-force,
.black-text-force * {
    color: #000000 !important;
    text-shadow: none !important;
}

/* Черный текст с сохранением читаемости на темных фонах */
.black-text-smart {
    color: #000000 !important;
}

.dark-section .black-text-smart,
.luxury-hero .black-text-smart,
.bg-luxury-hero .black-text-smart {
    color: #ffffff !important; /* Белый на темном фоне для читаемости */
}

.light-section .black-text-smart,
.bg-light .black-text-smart,
.bg-white .black-text-smart {
    color: #000000 !important; /* Черный на светлом фоне */
}

/* ===== БЕЛЫЙ ТЕКСТ ДЛЯ ЭЛЕМЕНТОВ ===== */

/* Принудительный белый цвет для любых колонок */
.white-text-column,
.white-text-column *,
.col-white-text,
.col-white-text * {
    color: #ffffff !important;
}

/* Белый текст для таблиц */
.table-white-text,
.table-white-text *,
.table-white-text th,
.table-white-text td,
.table-white-text thead,
.table-white-text tbody,
.table-white-text tfoot {
    color: #ffffff !important;
}

/* Белый текст для конкретных колонок таблиц */
.table .col-white,
.table .white-column,
table .col-white,
table .white-column {
    color: #ffffff !important;
}

/* Белый текст для статистических карточек */
.stats-card.white-text,
.stats-card.white-text *,
.stats-card.white-text .stats-value,
.stats-card.white-text .stats-label,
.stats-card.white-text .stats-change {
    color: #ffffff !important;
}

/* Белый текст для инвестиционных карточек */
.investment-card.white-text,
.investment-card.white-text *,
.investment-card.white-text .investment-title,
.investment-card.white-text .investment-amount,
.investment-card.white-text .investment-stats {
    color: #ffffff !important;
}

/* Белый текст для карточек лидерборда */
.leaderboard-item.white-text,
.leaderboard-item.white-text *,
.leaderboard-item.white-text .leader-name,
.leaderboard-item.white-text .leader-stats {
    color: #ffffff !important;
}

/* Белый текст для Bootstrap колонок */
.col-1.white-text, .col-1.white-text *,
.col-2.white-text, .col-2.white-text *,
.col-3.white-text, .col-3.white-text *,
.col-4.white-text, .col-4.white-text *,
.col-5.white-text, .col-5.white-text *,
.col-6.white-text, .col-6.white-text *,
.col-7.white-text, .col-7.white-text *,
.col-8.white-text, .col-8.white-text *,
.col-9.white-text, .col-9.white-text *,
.col-10.white-text, .col-10.white-text *,
.col-11.white-text, .col-11.white-text *,
.col-12.white-text, .col-12.white-text *,
.col-lg-1.white-text, .col-lg-1.white-text *,
.col-lg-2.white-text, .col-lg-2.white-text *,
.col-lg-3.white-text, .col-lg-3.white-text *,
.col-lg-4.white-text, .col-lg-4.white-text *,
.col-lg-5.white-text, .col-lg-5.white-text *,
.col-lg-6.white-text, .col-lg-6.white-text *,
.col-lg-7.white-text, .col-lg-7.white-text *,
.col-lg-8.white-text, .col-lg-8.white-text *,
.col-lg-9.white-text, .col-lg-9.white-text *,
.col-lg-10.white-text, .col-lg-10.white-text *,
.col-lg-11.white-text, .col-lg-11.white-text *,
.col-lg-12.white-text, .col-lg-12.white-text *,
.col-md-1.white-text, .col-md-1.white-text *,
.col-md-2.white-text, .col-md-2.white-text *,
.col-md-3.white-text, .col-md-3.white-text *,
.col-md-4.white-text, .col-md-4.white-text *,
.col-md-5.white-text, .col-md-5.white-text *,
.col-md-6.white-text, .col-md-6.white-text *,
.col-md-7.white-text, .col-md-7.white-text *,
.col-md-8.white-text, .col-md-8.white-text *,
.col-md-9.white-text, .col-md-9.white-text *,
.col-md-10.white-text, .col-md-10.white-text *,
.col-md-11.white-text, .col-md-11.white-text *,
.col-md-12.white-text, .col-md-12.white-text * {
    color: #ffffff !important;
}

/* Белый текст для заголовков */
.heading-white,
.heading-white *,
h1.white-text,
h2.white-text,
h3.white-text,
h4.white-text,
h5.white-text,
h6.white-text {
    color: #ffffff !important;
}

/* Белый текст для навигации */
.navbar.white-text,
.navbar.white-text *,
.nav.white-text,
.nav.white-text *,
.nav-link.white-text,
.navbar-nav.white-text,
.navbar-nav.white-text * {
    color: #ffffff !important;
}

/* Белый текст для кнопок */
.btn.white-text,
.btn.white-text *,
.button.white-text,
.button.white-text * {
    color: #ffffff !important;
}

/* Белый текст для конкретных элементов */
.white-text-force,
.white-text-force * {
    color: #ffffff !important;
}

/* Белый текст с сохранением читаемости на светлых фонах */
.white-text-smart {
    color: #ffffff !important;
}

.light-section .white-text-smart,
.bg-light .white-text-smart,
.bg-white .white-text-smart {
    color: #000000 !important; /* Черный на светлом фоне для читаемости */
}

.dark-section .white-text-smart,
.luxury-hero .white-text-smart,
.bg-luxury-hero .white-text-smart {
    color: #ffffff !important; /* Белый на темном фоне */
}

/* Белый текст для параграфов и текстовых элементов */
p.white-text,
span.white-text,
div.white-text,
.text-white-force,
.paragraph-white {
    color: #ffffff !important;
}

/* Белый текст для списков */
ul.white-text,
ul.white-text *,
ol.white-text,
ol.white-text *,
li.white-text {
    color: #ffffff !important;
}

/* Белый текст для форм */
.form-control.white-text,
.form-select.white-text,
.form-label.white-text,
label.white-text,
input.white-text,
textarea.white-text,
select.white-text {
    color: #ffffff !important;
}

/* Белый текст для карточек */
.card.white-text,
.card.white-text *,
.card-body.white-text,
.card-body.white-text *,
.card-header.white-text,
.card-header.white-text *,
.card-footer.white-text,
.card-footer.white-text * {
    color: #ffffff !important;
}

/* Белый текст для алертов и уведомлений */
.alert.white-text,
.alert.white-text *,
.notification.white-text,
.notification.white-text *,
.badge.white-text,
.badge.white-text * {
    color: #ffffff !important;
}

/* Белый текст для хлебных крошек */
.breadcrumb.white-text,
.breadcrumb.white-text *,
.breadcrumb-item.white-text {
    color: #ffffff !important;
}

/* Белый текст для пагинации */
.pagination.white-text,
.pagination.white-text *,
.page-link.white-text {
    color: #ffffff !important;
}

/* Белый текст для модальных окон */
.modal.white-text,
.modal.white-text *,
.modal-header.white-text,
.modal-header.white-text *,
.modal-body.white-text,
.modal-body.white-text *,
.modal-footer.white-text,
.modal-footer.white-text * {
    color: #ffffff !important;
}
