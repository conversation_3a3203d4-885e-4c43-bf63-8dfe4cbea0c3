/* ===== МОБИЛЬНЫЕ СТИЛИ GREENCHAIN ECOFUND - ПРЕМИУМ ДИЗАЙН ===== */

/* ===== АДАПТИВНЫЕ КОНТЕЙНЕРЫ ===== */
@media (max-width: 1399.98px) {
    .container {
        max-width: 1200px;
    }

    .hero-title {
        font-size: 3.5rem;
    }
}

@media (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .navbar-nav {
        gap: 0.5rem;
    }

    .investment-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 991.98px) {
    .container {
        max-width: 720px;
        padding: 0 1.5rem;
    }

    /* ===== HERO СЕКЦИЯ ===== */
    .hero-section {
        padding: 80px 0 60px;
    }

    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-stats .stat-value {
        font-size: 1.25rem;
    }

    .hero-stats .stat-label {
        font-size: 0.75rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem !important;
    }

    .btn-lg {
        padding: 0.875rem 2rem;
        font-size: 1rem;
    }

    /* ===== НАВИГАЦИЯ ===== */
    .navbar-toggler {
        display: block;
        border: none;
        background: transparent;
        color: var(--text-light);
    }

    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--dark-bg);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        margin-top: 0.5rem;
        padding: 1.5rem;
        z-index: 1000;
    }
    }
    
    .navbar-collapse.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .navbar-nav {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }
    
    .nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        padding: 0.75rem 1rem !important;
        border-radius: var(--radius-lg);
        border: 1px solid transparent;
        font-weight: 500;
    }

    .nav-link:hover,
    .nav-link.active {
        color: var(--text-light) !important;
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        transform: none;
    }
    
    .card-deck {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }
    
    .card-columns {
        column-count: 2;
        column-gap: 1rem;
    }
}

@media (max-width: 767.98px) {
    .container {
        max-width: 540px;
        padding: 0 1rem;
    }

    /* ===== HERO СЕКЦИЯ ===== */
    .hero-section {
        padding: 60px 0 40px;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .hero-stats {
        margin-bottom: 1.5rem;
    }

    .hero-stats .stat-value {
        font-size: 1.125rem;
    }

    .hero-stats .stat-label {
        font-size: 0.7rem;
    }

    /* ===== ТИПОГРАФИКА ===== */
    .section-title {
        font-size: 1.75rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    /* ===== КАРТОЧКИ ===== */
    .investment-card {
        margin-bottom: 1.5rem;
    }

    .investment-content {
        padding: 1.25rem;
    }

    .investment-title {
        font-size: 0.9rem;
        line-height: 1.3;
    }

    /* ===== СТАТИСТИКА ===== */
    .stats-card {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }

    .stats-value {
        font-size: 1.75rem;
    }

    /* ===== ПАРТНЕРЫ ===== */
    .partners-logos {
        gap: 1.5rem !important;
        flex-direction: column;
        text-align: center;
    }

    .partner-logo {
        font-size: 0.9rem;
    }
}

/* ===== ЭКСТРА МАЛЕНЬКИЕ ЭКРАНЫ ===== */
@media (max-width: 575.98px) {
    .container {
        padding: 0 0.75rem;
    }

    /* ===== HERO СЕКЦИЯ ===== */
    .hero-section {
        padding: 40px 0 30px;
    }

    .hero-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .hero-subtitle {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .hero-stats .row {
        gap: 0.5rem;
    }

    .hero-stats .stat-value {
        font-size: 1rem;
    }

    .hero-stats .stat-label {
        font-size: 0.65rem;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    /* ===== КАРТОЧКИ ===== */
    .investment-card {
        margin-bottom: 1rem;
    }

    .investment-content {
        padding: 1rem;
    }

    .investment-title {
        font-size: 0.85rem;
    }

    .investment-image {
        height: 150px;
    }

    /* ===== СЕКЦИИ ===== */
    .section-title {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
        margin-bottom: 2rem;
    }

    /* ===== СТАТИСТИКА ===== */
    .stats-card {
        padding: 1.25rem 0.75rem;
    }

    .stats-value {
        font-size: 1.5rem;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    /* ===== ПАРТНЕРЫ ===== */
    .partners-logos {
        gap: 1rem !important;
    }

    .partner-logo {
        font-size: 0.8rem;
    }

    /* ===== НАВИГАЦИЯ ===== */
    .navbar-brand {
        font-size: 1.125rem;
    }

    .navbar-collapse {
        padding: 1rem;
    }
    
    /* ===== НАВИГАЦИЯ ===== */
    .navbar {
        height: 70px;
    }
    
    .navbar-brand {
        font-size: var(--text-lg);
    }
    
    .navbar-brand .logo {
        height: 32px;
        width: 32px;
    }
    
    /* ===== КНОПКИ ===== */
    .btn {
        width: 100%;
        margin-bottom: 0.75rem;
        padding: 0.875rem 1.25rem;
        font-size: var(--text-base);
    }
    
    .btn-sm {
        width: auto;
        margin-bottom: 0.5rem;
        padding: 0.625rem 1rem;
        font-size: var(--text-sm);
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: var(--radius-xl);
        margin-left: 0;
        margin-bottom: 0.5rem;
    }
    
    .btn-group .btn:first-child,
    .btn-group .btn:last-child {
        border-radius: var(--radius-xl);
    }
    
    .btn-floating {
        bottom: 1rem;
        right: 1rem;
        width: 48px;
        height: 48px;
    }
    
    /* ===== КАРТОЧКИ ===== */
    .card {
        margin-bottom: 1.5rem;
        border-radius: var(--radius-xl);
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-header {
        padding: 1rem;
        font-size: var(--text-base);
    }
    
    .card-footer {
        padding: 0.75rem 1rem;
    }
    
    .card-deck {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .card-columns {
        column-count: 1;
    }
    
    .card-stats .stats-number {
        font-size: var(--text-2xl);
    }
    
    .card-feature .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--text-xl);
        margin-bottom: 1rem;
    }
    
    /* ===== ФОРМЫ ===== */
    .form-control {
        font-size: 16px; /* Предотвращает зум на iOS */
        padding: 1rem;
    }
    
    .form-control-sm {
        font-size: 14px;
        padding: 0.75rem;
    }
    
    .form-control-lg {
        font-size: 18px;
        padding: 1.25rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .input-group > .form-control,
    .input-group > .input-group-text {
        border-radius: var(--radius-xl);
        border: 2px solid var(--gray-300);
        margin-bottom: 0.5rem;
    }
    
    .input-group > .form-control:last-child,
    .input-group > .input-group-text:last-child {
        margin-bottom: 0;
    }
    
    /* ===== УТИЛИТЫ ===== */
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
    
    .text-md-center { text-align: center !important; }
    .text-md-left { text-align: left !important; }
    .text-md-right { text-align: right !important; }
    
    .w-md-100 { width: 100% !important; }
    .w-md-auto { width: auto !important; }
    .h-md-auto { height: auto !important; }
    
    .flex-md-column { flex-direction: column !important; }
    .flex-md-row { flex-direction: row !important; }
    
    .justify-content-md-center { justify-content: center !important; }
    .align-items-md-center { align-items: center !important; }
    
    /* Отступы */
    .p-md-0 { padding: 0 !important; }
    .p-md-1 { padding: 0.25rem !important; }
    .p-md-2 { padding: 0.5rem !important; }
    .p-md-3 { padding: 1rem !important; }
    .p-md-4 { padding: 1.5rem !important; }
    .p-md-5 { padding: 3rem !important; }
    
    .m-md-0 { margin: 0 !important; }
    .m-md-1 { margin: 0.25rem !important; }
    .m-md-2 { margin: 0.5rem !important; }
    .m-md-3 { margin: 1rem !important; }
    .m-md-4 { margin: 1.5rem !important; }
    .m-md-5 { margin: 3rem !important; }
    
    .mb-md-0 { margin-bottom: 0 !important; }
    .mb-md-1 { margin-bottom: 0.25rem !important; }
    .mb-md-2 { margin-bottom: 0.5rem !important; }
    .mb-md-3 { margin-bottom: 1rem !important; }
    .mb-md-4 { margin-bottom: 1.5rem !important; }
    .mb-md-5 { margin-bottom: 3rem !important; }
}

@media (max-width: 575.98px) {
    .container {
        padding: 0 0.75rem;
    }
    
    /* Еще более компактные стили для очень маленьких экранов */
    .page-title {
        font-size: var(--text-2xl);
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn {
        padding: 0.75rem 1rem;
        font-size: var(--text-sm);
    }
    
    .form-control {
        padding: 0.875rem;
    }
    
    .navbar-brand {
        font-size: var(--text-base);
    }
    
    .navbar-brand .logo {
        height: 28px;
        width: 28px;
    }
}

/* ===== ЛАНДШАФТНАЯ ОРИЕНТАЦИЯ ===== */
@media (max-width: 767.98px) and (orientation: landscape) {
    .navbar {
        height: 60px;
    }
    
    .hero-section {
        min-height: 60vh;
    }
    
    .modal-dialog {
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* ===== АНИМАЦИИ ДЛЯ МОБИЛЬНЫХ ===== */
@media (max-width: 767.98px) {
    .card {
        animation: slideInUp 0.6s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            transform: translateY(30px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    .btn:active {
        transform: scale(0.98);
    }
}

/* ===== ДОСТУПНОСТЬ ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
