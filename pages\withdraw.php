<?php
$page_title = "Вывод средств";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();
$available_balance = getAvailableBalance($user['id']);
$min_withdrawal = getSetting('min_withdrawal', 10);
$max_withdrawal_daily = getSetting('max_withdrawal_daily', 10000);

// Получение истории выводов
$withdrawal_history = getWithdrawalHistory($user['id']);

// Обработка формы вывода
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = processWithdrawal();
    if ($result['success']) {
        redirect('index.php?page=withdraw', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-money-bill-wave text-success"></i> Вывод средств
                </h2>
                <p class="page-subtitle">Выведите заработанные средства на ваш счет</p>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Форма вывода -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card"></i> Заявка на вывод
                    </h5>
                </div>
                
                <div class="card-body">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">Сумма вывода *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control amount-input" id="amount" 
                                           name="amount" step="0.01" min="<?php echo $min_withdrawal; ?>" 
                                           max="<?php echo min($available_balance, $max_withdrawal_daily); ?>" 
                                           required>
                                </div>
                                <div class="form-text">
                                    Минимум: $<?php echo number_format($min_withdrawal, 2); ?>, 
                                    максимум в день: $<?php echo number_format($max_withdrawal_daily, 2); ?>
                                </div>
                                <div class="invalid-feedback">
                                    Введите корректную сумму для вывода
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">Способ вывода *</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">Выберите способ</option>
                                    <option value="bank_card">Банковская карта</option>
                                    <option value="bank_transfer">Банковский перевод</option>
                                    <option value="paypal">PayPal</option>
                                    <option value="crypto_btc">Bitcoin (BTC)</option>
                                    <option value="crypto_eth">Ethereum (ETH)</option>
                                    <option value="crypto_usdt">Tether (USDT)</option>
                                </select>
                                <div class="invalid-feedback">
                                    Выберите способ вывода
                                </div>
                            </div>
                        </div>
                        
                        <!-- Быстрые суммы -->
                        <div class="quick-amounts mb-3">
                            <label class="form-label">Быстрый выбор:</label>
                            <div class="amount-buttons">
                                <button type="button" class="btn btn-outline-primary btn-sm amount-btn" data-amount="<?php echo $min_withdrawal; ?>">$<?php echo $min_withdrawal; ?></button>
                                <button type="button" class="btn btn-outline-primary btn-sm amount-btn" data-amount="50">$50</button>
                                <button type="button" class="btn btn-outline-primary btn-sm amount-btn" data-amount="100">$100</button>
                                <button type="button" class="btn btn-outline-primary btn-sm amount-btn" data-amount="500">$500</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setMaxAmount()">Максимум</button>
                            </div>
                        </div>
                        
                        <!-- Реквизиты для вывода -->
                        <div id="payment_details">
                            <!-- Банковская карта -->
                            <div class="payment-method-fields" id="bank_card_fields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="card_number" class="form-label">Номер карты</label>
                                        <input type="text" class="form-control" id="card_number" name="card_number" 
                                               placeholder="1234 5678 9012 3456" maxlength="19">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="card_holder" class="form-label">Держатель карты</label>
                                        <input type="text" class="form-control" id="card_holder" name="card_holder" 
                                               placeholder="IVAN PETROV">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Банковский перевод -->
                            <div class="payment-method-fields" id="bank_transfer_fields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="bank_name" class="form-label">Название банка</label>
                                        <input type="text" class="form-control" id="bank_name" name="bank_name">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="account_number" class="form-label">Номер счета</label>
                                        <input type="text" class="form-control" id="account_number" name="account_number">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- PayPal -->
                            <div class="payment-method-fields" id="paypal_fields" style="display: none;">
                                <div class="mb-3">
                                    <label for="paypal_email" class="form-label">Email PayPal</label>
                                    <input type="email" class="form-control" id="paypal_email" name="paypal_email">
                                </div>
                            </div>
                            
                            <!-- Криптовалюты -->
                            <div class="payment-method-fields" id="crypto_fields" style="display: none;">
                                <div class="mb-3">
                                    <label for="crypto_address" class="form-label">Адрес кошелька</label>
                                    <input type="text" class="form-control" id="crypto_address" name="crypto_address" 
                                           placeholder="Введите адрес кошелька">
                                    <div class="form-text">
                                        Убедитесь, что адрес указан правильно. Неверный адрес может привести к потере средств.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="comment" class="form-label">Комментарий</label>
                            <textarea class="form-control" id="comment" name="comment" rows="3" 
                                      placeholder="Дополнительная информация (необязательно)"></textarea>
                        </div>
                        
                        <!-- Комиссия -->
                        <div class="commission-info mb-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Информация о комиссии:</h6>
                                <ul class="mb-0">
                                    <li>Банковская карта: 2%</li>
                                    <li>Банковский перевод: 1%</li>
                                    <li>PayPal: 3%</li>
                                    <li>Криптовалюты: 1%</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="confirm_withdrawal" required>
                            <label class="form-check-label" for="confirm_withdrawal">
                                Я подтверждаю правильность указанных реквизитов и согласен с условиями вывода средств
                            </label>
                            <div class="invalid-feedback">
                                Необходимо подтвердить операцию
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-paper-plane"></i> Подать заявку на вывод
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Информация о балансе -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-wallet"></i> Баланс
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="balance-item">
                        <div class="balance-label">Общий баланс</div>
                        <div class="balance-value"><?php echo formatMoney($user['balance']); ?></div>
                    </div>
                    <hr>
                    <div class="balance-item">
                        <div class="balance-label">Доступно для вывода</div>
                        <div class="balance-value text-success"><?php echo formatMoney($available_balance); ?></div>
                    </div>
                </div>
            </div>
            
            <!-- Статистика выводов -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> Статистика
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    $withdrawal_stats = getWithdrawalStats($user['id']);
                    ?>
                    <div class="stat-item">
                        <span class="stat-label">Всего выведено:</span>
                        <span class="stat-value"><?php echo formatMoney($withdrawal_stats['total_withdrawn']); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Заявок в обработке:</span>
                        <span class="stat-value"><?php echo $withdrawal_stats['pending_count']; ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Последний вывод:</span>
                        <span class="stat-value"><?php echo $withdrawal_stats['last_withdrawal'] ?: 'Нет'; ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- История выводов -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> История выводов
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($withdrawal_history)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>У вас пока нет заявок на вывод средств</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Дата</th>
                                        <th>Сумма</th>
                                        <th>Способ</th>
                                        <th>Статус</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($withdrawal_history as $withdrawal): ?>
                                        <tr>
                                            <td><?php echo date('d.m.Y H:i', strtotime($withdrawal['created_at'])); ?></td>
                                            <td><?php echo formatMoney($withdrawal['amount']); ?></td>
                                            <td><?php echo getPaymentMethodName($withdrawal['payment_method']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo getStatusColor($withdrawal['status']); ?>">
                                                    <?php echo getStatusName($withdrawal['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="showWithdrawalDetails(<?php echo $withdrawal['id']; ?>)">
                                                    <i class="fas fa-eye"></i> Детали
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.amount-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.payment-method-fields {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.balance-item {
    margin-bottom: 1rem;
}

.balance-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.balance-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
}

@media (max-width: 768px) {
    .amount-buttons {
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Обработка изменения способа вывода
    document.getElementById('payment_method').addEventListener('change', function() {
        const method = this.value;
        
        // Скрываем все поля
        document.querySelectorAll('.payment-method-fields').forEach(field => {
            field.style.display = 'none';
        });
        
        // Показываем нужные поля
        if (method === 'bank_card') {
            document.getElementById('bank_card_fields').style.display = 'block';
        } else if (method === 'bank_transfer') {
            document.getElementById('bank_transfer_fields').style.display = 'block';
        } else if (method === 'paypal') {
            document.getElementById('paypal_fields').style.display = 'block';
        } else if (method.startsWith('crypto_')) {
            document.getElementById('crypto_fields').style.display = 'block';
        }
    });
    
    // Быстрые суммы
    document.querySelectorAll('.amount-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.dataset.amount;
            document.getElementById('amount').value = amount;
        });
    });
    
    // Форматирование номера карты
    document.getElementById('card_number').addEventListener('input', function() {
        let value = this.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
        let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
        this.value = formattedValue;
    });
});

function setMaxAmount() {
    const maxAmount = <?php echo min($available_balance, $max_withdrawal_daily); ?>;
    document.getElementById('amount').value = maxAmount;
}

function showWithdrawalDetails(id) {
    // Здесь можно реализовать показ деталей заявки
    alert('Детали заявки #' + id);
}
</script>

<?php
/**
 * Получение доступного для вывода баланса
 */
function getAvailableBalance($user_id) {
    global $conn;
    
    // Пока просто возвращаем общий баланс
    // В будущем можно добавить логику блокировки средств
    $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    
    return $result ? $result['balance'] : 0;
}

/**
 * Получение истории выводов
 */
function getWithdrawalHistory($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT * FROM transactions 
        WHERE user_id = ? AND type = 'withdrawal' 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetchAll();
}

/**
 * Получение статистики выводов
 */
function getWithdrawalStats($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawn,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
            MAX(CASE WHEN status = 'completed' THEN DATE(created_at) END) as last_withdrawal
        FROM transactions 
        WHERE user_id = ? AND type = 'withdrawal'
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetch();
}

/**
 * Обработка заявки на вывод
 */
function processWithdrawal() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $amount = floatval($_POST['amount']);
    $payment_method = sanitizeInput($_POST['payment_method']);
    $comment = sanitizeInput($_POST['comment']);
    $user_id = $_SESSION['user_id'];
    
    // Сбор реквизитов в зависимости от способа
    $payment_details = [];
    switch ($payment_method) {
        case 'bank_card':
            $payment_details = [
                'card_number' => sanitizeInput($_POST['card_number']),
                'card_holder' => sanitizeInput($_POST['card_holder'])
            ];
            break;
        case 'bank_transfer':
            $payment_details = [
                'bank_name' => sanitizeInput($_POST['bank_name']),
                'account_number' => sanitizeInput($_POST['account_number'])
            ];
            break;
        case 'paypal':
            $payment_details = [
                'paypal_email' => sanitizeInput($_POST['paypal_email'])
            ];
            break;
        default:
            if (strpos($payment_method, 'crypto_') === 0) {
                $payment_details = [
                    'crypto_address' => sanitizeInput($_POST['crypto_address'])
                ];
            }
    }
    
    // Валидация
    $min_withdrawal = getSetting('min_withdrawal', 10);
    $max_withdrawal_daily = getSetting('max_withdrawal_daily', 10000);
    
    if ($amount < $min_withdrawal) {
        return ['success' => false, 'message' => 'Минимальная сумма для вывода: $' . number_format($min_withdrawal, 2)];
    }
    
    if ($amount > $max_withdrawal_daily) {
        return ['success' => false, 'message' => 'Максимальная сумма вывода в день: $' . number_format($max_withdrawal_daily, 2)];
    }
    
    try {
        $conn->beginTransaction();
        
        // Проверка баланса
        $user = getCurrentUser();
        $available_balance = getAvailableBalance($user_id);
        
        if ($amount > $available_balance) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Недостаточно средств для вывода'];
        }
        
        // Проверка дневного лимита
        $stmt = $conn->prepare("
            SELECT COALESCE(SUM(amount), 0) as daily_total
            FROM transactions 
            WHERE user_id = ? AND type = 'withdrawal' 
            AND DATE(created_at) = CURDATE()
            AND status IN ('pending', 'completed')
        ");
        $stmt->execute([$user_id]);
        $daily_total = $stmt->fetch()['daily_total'];
        
        if (($daily_total + $amount) > $max_withdrawal_daily) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Превышен дневной лимит вывода'];
        }
        
        // Списание с баланса
        $new_balance = $user['balance'] - $amount;
        $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
        $stmt->execute([$new_balance, $user_id]);
        
        // Создание транзакции
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description, status, payment_method, payment_details) 
            VALUES (?, 'withdrawal', ?, ?, ?, ?, 'pending', ?, ?)
        ");
        $stmt->execute([
            $user_id,
            $amount,
            $user['balance'],
            $new_balance,
            'Заявка на вывод средств' . ($comment ? ': ' . $comment : ''),
            $payment_method,
            json_encode($payment_details)
        ]);
        
        // Логирование
        logAction('withdrawal_requested', "Amount: $amount, Method: $payment_method");
        
        $conn->commit();
        
        return [
            'success' => true,
            'message' => 'Заявка на вывод средств принята! Обработка займет 1-3 рабочих дня.'
        ];
        
    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Withdrawal error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обработке заявки. Попробуйте позже.'];
    }
}



/**
 * Получение названия способа оплаты
 */
function getPaymentMethodName($method) {
    $methods = [
        'bank_card' => 'Банковская карта',
        'bank_transfer' => 'Банковский перевод',
        'paypal' => 'PayPal',
        'crypto_btc' => 'Bitcoin',
        'crypto_eth' => 'Ethereum',
        'crypto_usdt' => 'Tether'
    ];
    
    return $methods[$method] ?? $method;
}

/**
 * Получение цвета статуса
 */
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'completed' => 'success',
        'failed' => 'danger',
        'cancelled' => 'secondary'
    ];
    
    return $colors[$status] ?? 'secondary';
}

/**
 * Получение названия статуса
 */
function getStatusName($status) {
    $names = [
        'pending' => 'В обработке',
        'completed' => 'Выполнено',
        'failed' => 'Отклонено',
        'cancelled' => 'Отменено'
    ];
    
    return $names[$status] ?? $status;
}
?>
