/* ===== GREENCHAIN ECOFUND - ПРЕМИАЛЬНАЯ ТЕМНАЯ СХЕМА ===== */

/* ===== ГЛОБАЛЬНЫЕ CSS ПЕРЕМЕННЫЕ ТЕМНОЙ СХЕМЫ ===== */
:root {
    /* Основные темные цвета */
    --dark-black: #000000;
    --dark-charcoal: #1a1a1a;
    --dark-navy: #1e3a8a;
    --dark-navy-deep: #1a365d;
    --dark-green: #1a3d2e;
    --dark-green-medium: #2d5a3d;
    --dark-green-deep: #0f2419;
    
    /* Золотые акценты */
    --gold-primary: #d4af37;
    --gold-bright: #ffd700;
    --gold-dark: #b8941f;
    --gold-pale: #f4e4a6;
    
    /* Текстовые цвета */
    --text-white: #ffffff;
    --text-light-gray: #e2e8f0;
    --text-medium-gray: #94a3b8;
    --text-dark-gray: #64748b;
    
    /* Фоновые цвета */
    --bg-dark-primary: rgba(0, 0, 0, 0.95);
    --bg-dark-secondary: rgba(26, 26, 26, 0.9);
    --bg-dark-card: rgba(45, 45, 45, 0.8);
    --bg-dark-input: rgba(45, 45, 45, 0.8);
    
    /* Границы и тени */
    --border-gold: rgba(212, 175, 55, 0.3);
    --border-gold-hover: rgba(212, 175, 55, 0.5);
    --shadow-gold: rgba(212, 175, 55, 0.2);
    --shadow-dark: rgba(0, 0, 0, 0.3);
    
    /* Градиенты */
    --gradient-primary: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #1e3a8a 100%);
    --gradient-secondary: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    --gradient-gold: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
    --gradient-card: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #1e3a8a 100%);
    --gradient-eco: linear-gradient(135deg, #1a3d2e 0%, #2d5a3d 50%, #0f2419 100%);
}

/* ===== ГЛОБАЛЬНЫЕ ПЕРЕОПРЕДЕЛЕНИЯ ===== */

/* Основной фон страницы */
body {
    background: var(--gradient-primary) !important;
    color: var(--text-white) !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    min-height: 100vh;
}

/* Контейнеры */
.container,
.container-fluid {
    color: var(--text-white) !important;
}

/* ===== КАРТОЧКИ И ПАНЕЛИ ===== */
.card {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 16px !important;
    box-shadow: 0 10px 30px var(--shadow-dark), 0 0 20px var(--shadow-gold) !important;
    transition: all 0.3s ease;
    color: var(--text-white) !important;
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(212, 175, 55, 0.3) !important;
    border-color: var(--border-gold-hover) !important;
}

.card-header {
    background: var(--gradient-secondary) !important;
    border-bottom: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 16px 16px 0 0 !important;
}

.card-body {
    background: transparent !important;
    color: var(--text-white) !important;
}

.card-footer {
    background: var(--gradient-secondary) !important;
    border-top: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 0 0 16px 16px !important;
}

.card-title {
    color: var(--text-white) !important;
    font-weight: 700;
}

/* Белый текст для всех элементов в карточках */
.card *,
.card-header *,
.card-body *,
.card-footer * {
    color: var(--text-white) !important;
}

/* ===== КНОПКИ ===== */
.btn-primary {
    background: var(--gradient-gold) !important;
    border: none !important;
    color: var(--dark-black) !important;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
    color: var(--dark-black) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(212, 175, 55, 0.4);
}

.btn-outline-primary {
    border-color: var(--gold-primary) !important;
    color: var(--gold-primary) !important;
    background: transparent !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
    border-color: var(--gold-bright) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
    background: var(--bg-dark-card) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(212, 175, 55, 0.2) !important;
    border-color: var(--border-gold-hover) !important;
    color: var(--text-white) !important;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    background: var(--bg-dark-card) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--gold-primary) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: rgba(212, 175, 55, 0.2) !important;
    border-color: var(--gold-bright) !important;
    color: var(--gold-bright) !important;
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
    color: var(--text-white) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    border: none !important;
    color: var(--dark-black) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border: none !important;
    color: var(--text-white) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* ===== ФОРМЫ ===== */
.form-control {
    background: var(--bg-dark-input) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(45, 45, 45, 0.9) !important;
    border-color: var(--gold-primary) !important;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3) !important;
    color: var(--text-white) !important;
}

.form-control::placeholder {
    color: var(--text-medium-gray) !important;
}

.form-control[readonly] {
    background: rgba(30, 30, 30, 0.8) !important;
    border-color: rgba(212, 175, 55, 0.2) !important;
    color: var(--text-medium-gray) !important;
    cursor: not-allowed;
}

.form-select {
    background: var(--bg-dark-input) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-select:focus {
    background: rgba(45, 45, 45, 0.9) !important;
    border-color: var(--gold-primary) !important;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3) !important;
    color: var(--text-white) !important;
}

.form-select option {
    background: #2d2d2d !important;
    color: var(--text-white) !important;
}

.form-label {
    color: var(--text-light-gray) !important;
    font-weight: 600;
}

.form-text {
    color: var(--text-medium-gray) !important;
    font-size: 0.875rem;
    font-weight: 500;
}

textarea.form-control {
    background: var(--bg-dark-input) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 8px;
    resize: vertical;
    transition: all 0.3s ease;
}

textarea.form-control:focus {
    background: rgba(45, 45, 45, 0.9) !important;
    border-color: var(--gold-primary) !important;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3) !important;
    color: var(--text-white) !important;
}

/* ===== INPUT GROUPS ===== */
.input-group .form-control {
    border-right: none !important;
}

.input-group .btn {
    border-left: none !important;
    border-color: var(--border-gold) !important;
}

.input-group-text {
    background: rgba(212, 175, 55, 0.2) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--gold-primary) !important;
}

/* ===== АЛЕРТЫ ===== */
.alert {
    border-radius: 12px;
    border: none;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(72, 187, 120, 0.1) 100%) !important;
    color: #48bb78 !important;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(255, 99, 132, 0.1) 100%) !important;
    color: #ff6b6b !important;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(253, 126, 20, 0.1) 100%) !important;
    color: #ffd700 !important;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.2) 0%, rgba(32, 201, 151, 0.1) 100%) !important;
    color: #20c997 !important;
    border-left: 4px solid #17a2b8;
}

/* ===== ТАБЛИЦЫ ===== */
.table {
    color: var(--text-white) !important;
    background: transparent !important;
}

.table-dark {
    background: var(--bg-dark-secondary) !important;
    color: var(--text-white) !important;
}

.table thead th {
    background: var(--gradient-secondary) !important;
    color: var(--text-white) !important;
    border-color: var(--border-gold) !important;
    font-weight: 600;
}

.table tbody tr {
    background: rgba(26, 26, 26, 0.5) !important;
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(212, 175, 55, 0.1) !important;
    transform: translateX(5px);
}

.table td,
.table th {
    border-color: var(--border-gold) !important;
    color: var(--text-white) !important;
    padding: 1rem 0.75rem;
}

/* ===== ЗНАЧКИ И СТАТУСЫ ===== */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: var(--text-white) !important;
    border: 1px solid rgba(40, 167, 69, 0.3);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: var(--dark-black) !important;
    border: 1px solid rgba(255, 193, 7, 0.3);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: var(--text-white) !important;
    border: 1px solid rgba(220, 53, 69, 0.3);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%) !important;
    color: var(--text-white) !important;
    border: 1px solid rgba(23, 162, 184, 0.3);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
}

.badge.bg-primary {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
    border: 1px solid rgba(212, 175, 55, 0.3);
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.2);
}

.badge.bg-secondary {
    background: var(--gradient-secondary) !important;
    color: var(--text-white) !important;
    border: 1px solid var(--border-gold);
    box-shadow: 0 2px 8px var(--shadow-gold);
}

/* ===== ЦВЕТНОЙ ТЕКСТ ===== */
.text-primary {
    color: var(--gold-primary) !important;
}

.text-secondary {
    color: var(--text-light-gray) !important;
}

.text-success {
    color: #48bb78 !important;
    font-weight: 600;
}

.text-warning {
    color: var(--gold-bright) !important;
    font-weight: 600;
}

.text-danger {
    color: #ff6b6b !important;
    font-weight: 600;
}

.text-info {
    color: #20c997 !important;
    font-weight: 600;
}

.text-muted {
    color: var(--text-medium-gray) !important;
}

.text-white {
    color: var(--text-white) !important;
}

/* ===== НАВИГАЦИЯ ===== */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-gold);
    box-shadow: 0 2px 20px var(--shadow-dark);
}

.navbar-brand {
    color: var(--text-white) !important;
    font-weight: 700;
}

.navbar-nav .nav-link {
    color: var(--text-light-gray) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: var(--gold-bright) !important;
    background: rgba(212, 175, 55, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    color: var(--gold-primary) !important;
    background: rgba(212, 175, 55, 0.2);
}

.navbar-toggler {
    border-color: var(--border-gold) !important;
    color: var(--gold-primary) !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3) !important;
}

/* ===== МОДАЛЬНЫЕ ОКНА ===== */
.modal-content {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 16px !important;
    box-shadow: 0 20px 40px var(--shadow-dark), 0 0 30px var(--shadow-gold) !important;
    backdrop-filter: blur(15px);
}

.modal-header {
    background: var(--gradient-secondary) !important;
    border-bottom: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 16px 16px 0 0 !important;
}

.modal-body {
    background: transparent !important;
    color: var(--text-white) !important;
}

.modal-footer {
    background: var(--gradient-secondary) !important;
    border-top: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    border-radius: 0 0 16px 16px !important;
}

.modal-title {
    color: var(--text-white) !important;
    font-weight: 700;
}

.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* ===== ПРОГРЕСС БАРЫ ===== */
.progress {
    background: rgba(45, 45, 45, 0.8) !important;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progress-bar {
    background: var(--gradient-gold) !important;
    transition: all 0.3s ease;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.progress-bar.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.progress-bar.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.progress-bar.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

/* ===== ПАГИНАЦИЯ ===== */
.pagination .page-link {
    background: var(--bg-dark-card) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: rgba(212, 175, 55, 0.2) !important;
    border-color: var(--gold-bright) !important;
    color: var(--gold-bright) !important;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: var(--gradient-gold) !important;
    border-color: var(--gold-primary) !important;
    color: var(--dark-black) !important;
}

/* ===== ДРОПДАУНЫ ===== */
.dropdown-menu {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px var(--shadow-dark), 0 0 20px var(--shadow-gold) !important;
    backdrop-filter: blur(10px);
}

.dropdown-item {
    color: var(--text-white) !important;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: rgba(212, 175, 55, 0.2) !important;
    color: var(--gold-bright) !important;
}

.dropdown-item.active {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
}

/* ===== АККОРДЕОНЫ ===== */
.accordion-item {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 12px !important;
    margin-bottom: 1rem;
}

.accordion-header .accordion-button {
    background: var(--gradient-secondary) !important;
    color: var(--text-white) !important;
    border: none !important;
    font-weight: 600;
}

.accordion-header .accordion-button:not(.collapsed) {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
}

.accordion-body {
    background: transparent !important;
    color: var(--text-white) !important;
}

/* ===== КАРУСЕЛИ ===== */
.carousel-control-prev,
.carousel-control-next {
    background: rgba(212, 175, 55, 0.2) !important;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    top: 50%;
    transform: translateY(-50%);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: rgba(212, 175, 55, 0.4) !important;
}

.carousel-indicators [data-bs-target] {
    background: var(--gold-primary) !important;
    border-radius: 50%;
    width: 12px;
    height: 12px;
}

/* ===== ТОСТЫ ===== */
.toast {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px var(--shadow-dark) !important;
    backdrop-filter: blur(10px);
}

.toast-header {
    background: var(--gradient-secondary) !important;
    color: var(--text-white) !important;
    border-bottom: 1px solid var(--border-gold) !important;
}

.toast-body {
    color: var(--text-white) !important;
}

/* ===== СПЕЦИАЛЬНЫЕ КОМПОНЕНТЫ ===== */

/* Статистические карточки */
.stats-card {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 16px !important;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-gold);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(212, 175, 55, 0.3) !important;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gold-primary) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-label {
    color: var(--text-light-gray) !important;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-change {
    font-size: 0.875rem;
    font-weight: 600;
}

.stats-change.positive {
    color: #48bb78 !important;
}

.stats-change.negative {
    color: #ff6b6b !important;
}

/* Приветственный заголовок */
.welcome-header {
    background: var(--gradient-eco) !important;
    padding: 2rem;
    border-radius: 16px;
    margin-bottom: 2rem;
    border: 1px solid var(--border-gold);
    box-shadow: 0 10px 30px var(--shadow-dark);
    text-align: center;
}

.welcome-title {
    color: var(--text-white) !important;
    font-weight: 700;
    font-size: 2.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--text-light-gray) !important;
    font-weight: 500;
    font-size: 1.1rem;
}

.wave {
    display: inline-block;
    animation: wave 2s infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(20deg); }
    75% { transform: rotate(-20deg); }
}

/* Инвестиционные карточки */
.investment-card {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 16px !important;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.investment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-gold);
}

.investment-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 40px rgba(212, 175, 55, 0.4) !important;
}

.investment-progress {
    background: rgba(45, 45, 45, 0.8) !important;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.investment-progress .progress-bar {
    background: var(--gradient-gold) !important;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4);
}

/* Кнопки действий */
.action-btn {
    background: var(--gradient-gold) !important;
    border: none !important;
    color: var(--dark-black) !important;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
    background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
}

/* ===== ФУТЕР ===== */
.footer {
    background: var(--gradient-primary) !important;
    border-top: 1px solid var(--border-gold);
    color: var(--text-white) !important;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer .widget-title {
    color: var(--gold-primary) !important;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer .footer-links a {
    color: var(--text-light-gray) !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer .footer-links a:hover {
    color: var(--gold-bright) !important;
    transform: translateX(5px);
}

.footer .social-link {
    background: rgba(212, 175, 55, 0.2) !important;
    color: var(--gold-primary) !important;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.footer .social-link:hover {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
    transform: translateY(-3px);
}

/* ===== ВАЛИДАЦИЯ ФОРМ ===== */
.invalid-feedback {
    color: #ff6b6b !important;
    font-weight: 500;
}

.valid-feedback {
    color: #48bb78 !important;
    font-weight: 500;
}

.is-invalid .form-control {
    border-color: #dc3545 !important;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.3) !important;
}

.is-valid .form-control {
    border-color: #28a745 !important;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.3) !important;
}

/* ===== АНИМАЦИИ И ЭФФЕКТЫ ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(212, 175, 55, 0.6);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1.5rem;
        border-radius: 12px !important;
    }

    .welcome-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .stats-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .stats-value {
        font-size: 1.5rem;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0 1rem;
    }

    .card {
        border-radius: 8px !important;
    }

    .welcome-title {
        font-size: 1.75rem;
    }

    .stats-value {
        font-size: 1.25rem;
    }

    .modal-dialog {
        margin: 1rem;
    }
}

/* ===== СКРОЛЛБАРЫ ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-dark-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-gold);
    border-radius: 4px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%);
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ УТИЛИТЫ ===== */
.text-gradient {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-secondary {
    background: var(--gradient-secondary) !important;
}

.bg-gradient-gold {
    background: var(--gradient-gold) !important;
}

.bg-gradient-eco {
    background: var(--gradient-eco) !important;
}

.border-gold {
    border-color: var(--border-gold) !important;
}

.shadow-gold {
    box-shadow: 0 4px 15px var(--shadow-gold) !important;
}

.shadow-dark {
    box-shadow: 0 4px 15px var(--shadow-dark) !important;
}

/* ===== ПЕРЕОПРЕДЕЛЕНИЯ BOOTSTRAP ===== */
.list-group-item {
    background: var(--bg-dark-card) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
}

.list-group-item:hover {
    background: rgba(212, 175, 55, 0.1) !important;
}

.list-group-item.active {
    background: var(--gradient-gold) !important;
    border-color: var(--gold-primary) !important;
    color: var(--dark-black) !important;
}

.breadcrumb {
    background: var(--bg-dark-card) !important;
    border-radius: 8px;
    padding: 0.75rem 1rem;
}

.breadcrumb-item a {
    color: var(--gold-primary) !important;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--text-light-gray) !important;
}

.offcanvas {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    color: var(--text-white) !important;
}

.offcanvas-header {
    border-bottom: 1px solid var(--border-gold) !important;
}

.offcanvas-title {
    color: var(--text-white) !important;
}

/* ===== СПЕЦИФИЧЕСКИЕ СТИЛИ ДЛЯ СТРАНИЦ ===== */

/* Hero Section */
.hero-section {
    background: var(--gradient-primary) !important;
    color: var(--text-white) !important;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(30, 58, 138, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-title {
    color: var(--text-white) !important;
    font-weight: 800;
    font-size: 3.5rem;
    line-height: 1.2;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    color: var(--text-light-gray) !important;
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.6;
}

.luxury-badge {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.luxury-stat-card {
    background: rgba(26, 26, 26, 0.8) !important;
    border: 1px solid var(--border-gold);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.luxury-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.2);
}

.luxury-stat-number {
    color: var(--gold-primary) !important;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.luxury-stat-label {
    color: var(--text-light-gray) !important;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Кнопки Hero */
.btn-luxury-primary {
    background: var(--gradient-gold) !important;
    border: none !important;
    color: var(--dark-black) !important;
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-luxury-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.6);
    background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
    color: var(--dark-black) !important;
}

.btn-luxury-secondary {
    background: transparent !important;
    border: 2px solid var(--gold-primary) !important;
    color: var(--gold-primary) !important;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-luxury-secondary:hover {
    background: var(--gradient-gold) !important;
    color: var(--dark-black) !important;
    border-color: var(--gold-bright) !important;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Формы авторизации */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary) !important;
    padding: 2rem 0;
}

.auth-card {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 40px var(--shadow-dark), 0 0 30px var(--shadow-gold) !important;
    backdrop-filter: blur(15px);
    overflow: hidden;
    max-width: 500px;
    width: 100%;
}

.auth-header {
    background: var(--gradient-secondary) !important;
    color: var(--text-white) !important;
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid var(--border-gold);
}

.auth-header h3 {
    color: var(--text-white) !important;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--text-light-gray) !important;
    margin: 0;
    font-weight: 500;
}

.auth-body {
    padding: 2rem;
    background: transparent !important;
}

.auth-footer {
    background: var(--gradient-secondary) !important;
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid var(--border-gold);
}

.auth-footer a {
    color: var(--gold-primary) !important;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-footer a:hover {
    color: var(--gold-bright) !important;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

/* Специальные стили для dashboard */
.balance-card {
    background: var(--gradient-eco) !important;
    border: 1px solid var(--border-gold) !important;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-gold);
}

.user-balance {
    font-size: 2.5rem !important;
    font-weight: 800 !important;
    color: var(--gold-primary) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: glow 3s ease-in-out infinite;
}

/* Инвестиционные планы */
.investment-plan {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 16px !important;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.investment-plan::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-gold);
}

.investment-plan:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 40px rgba(212, 175, 55, 0.4) !important;
}

.plan-title {
    color: var(--text-white) !important;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.plan-profit {
    color: var(--gold-primary) !important;
    font-size: 3rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.plan-features li {
    color: var(--text-light-gray) !important;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(212, 175, 55, 0.1);
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features li i {
    color: var(--gold-primary) !important;
    margin-right: 0.5rem;
}

/* Статистика и графики */
.chart-container {
    background: var(--gradient-card) !important;
    border: 1px solid var(--border-gold) !important;
    border-radius: 16px !important;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.chart-title {
    color: var(--text-white) !important;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}

/* Транзакции */
.transaction-item {
    background: rgba(26, 26, 26, 0.8) !important;
    border: 1px solid var(--border-gold);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: rgba(212, 175, 55, 0.1) !important;
    transform: translateX(5px);
}

.transaction-type {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transaction-amount {
    font-weight: 700;
    font-size: 1.1rem;
}

.transaction-amount.positive {
    color: #48bb78 !important;
}

.transaction-amount.negative {
    color: #ff6b6b !important;
}

.transaction-date {
    color: var(--text-medium-gray) !important;
    font-size: 0.875rem;
}
