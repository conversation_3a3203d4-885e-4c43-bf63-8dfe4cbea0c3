<?php
// API для создания заявки на вывод средств
// GreenChain EcoFund - Withdrawal Request API

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../includes/functions.php';

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Метод не разрешен']);
    exit;
}

// Проверка авторизации
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Необходима авторизация']);
    exit;
}

try {
    // Получение данных запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }

    // Валидация входных данных
    $amount = floatval($input['amount'] ?? 0);
    $currency = $input['currency'] ?? 'USDT';
    $network = $input['network'] ?? 'TRC-20';
    $wallet_address = trim($input['wallet_address'] ?? '');

    // Проверка обязательных полей
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Некорректная сумма']);
        exit;
    }

    if (empty($wallet_address)) {
        echo json_encode(['success' => false, 'message' => 'Необходимо указать адрес кошелька']);
        exit;
    }

    // Валидация TRC-20 адреса
    if (!isValidTRC20Address($wallet_address)) {
        echo json_encode(['success' => false, 'message' => 'Некорректный адрес TRC-20 кошелька']);
        exit;
    }

    // Получение настроек системы
    $min_withdrawal = floatval(getSetting('payment_min_withdrawal', 20));
    $max_withdrawal = floatval(getSetting('payment_max_withdrawal', 5000));
    $withdrawal_fee = floatval(getSetting('payment_withdrawal_fee', 2));
    $daily_limit = floatval(getSetting('payment_daily_withdrawal_limit', 1000));
    $system_enabled = getSetting('payment_system_enabled', 1);

    if (!$system_enabled) {
        echo json_encode(['success' => false, 'message' => 'Платежная система временно недоступна']);
        exit;
    }

    // Проверка лимитов
    if ($amount < $min_withdrawal) {
        echo json_encode(['success' => false, 'message' => "Минимальная сумма вывода: $min_withdrawal USDT"]);
        exit;
    }

    if ($amount > $max_withdrawal) {
        echo json_encode(['success' => false, 'message' => "Максимальная сумма вывода: $max_withdrawal USDT"]);
        exit;
    }

    // Получение текущего пользователя
    $user = getCurrentUser();
    $user_id = $user['id'];
    $user_balance = floatval($user['balance'] ?? 0);

    // Расчет итоговой суммы с комиссией
    $final_amount = $amount - $withdrawal_fee;
    
    if ($final_amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Сумма вывода должна покрывать комиссию']);
        exit;
    }

    // Проверка баланса пользователя
    if ($user_balance < $amount) {
        echo json_encode(['success' => false, 'message' => 'Недостаточно средств на балансе']);
        exit;
    }

    // Проверка дневного лимита
    $today_withdrawals = getTodayWithdrawals($user_id);
    if (($today_withdrawals + $amount) > $daily_limit) {
        $remaining = $daily_limit - $today_withdrawals;
        echo json_encode(['success' => false, 'message' => "Превышен дневной лимит. Доступно: $remaining USDT"]);
        exit;
    }

    // Проверка на активные заявки на вывод
    $active_requests = getActiveWithdrawalRequests($user_id);
    if ($active_requests > 0) {
        echo json_encode(['success' => false, 'message' => 'У вас уже есть активная заявка на вывод']);
        exit;
    }

    // Создание заявки на вывод
    $stmt = $conn->prepare("
        INSERT INTO withdrawal_requests 
        (user_id, amount, currency, network, wallet_address, fee_amount, final_amount, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
    ");

    if ($stmt->execute([$user_id, $amount, $currency, $network, $wallet_address, $withdrawal_fee, $final_amount])) {
        $request_id = $conn->lastInsertId();

        // Временная блокировка средств на балансе (опционально)
        // updateUserBalance($user_id, -$amount, "Блокировка средств для вывода #$request_id");

        // Логирование операции
        logTransaction($user_id, 'withdrawal_request', $amount, "Заявка на вывод #$request_id", $request_id, 'withdrawal_request');

        $response = [
            'success' => true,
            'message' => 'Заявка на вывод создана успешно',
            'request_id' => $request_id,
            'amount' => $amount,
            'fee_amount' => $withdrawal_fee,
            'final_amount' => $final_amount,
            'currency' => $currency,
            'network' => $network,
            'wallet_address' => $wallet_address,
            'status' => 'pending'
        ];

        echo json_encode($response);

    } else {
        echo json_encode(['success' => false, 'message' => 'Ошибка создания заявки']);
    }

} catch (Exception $e) {
    error_log("Withdrawal Request Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Внутренняя ошибка сервера']);
}

// Функция валидации TRC-20 адреса
function isValidTRC20Address($address) {
    // TRC-20 адреса начинаются с 'T' и имеют длину 34 символа
    if (strlen($address) !== 34) {
        return false;
    }
    
    if ($address[0] !== 'T') {
        return false;
    }
    
    // Проверка на допустимые символы (Base58)
    $pattern = '/^[**********************************************************]+$/';
    return preg_match($pattern, $address);
}

// Функция получения суммы выводов за сегодня
function getTodayWithdrawals($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT COALESCE(SUM(amount), 0) as total 
            FROM withdrawal_requests 
            WHERE user_id = ? 
            AND DATE(created_at) = CURDATE() 
            AND status IN ('pending', 'approved', 'completed')
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch();
        
        return floatval($result['total']);
    } catch (Exception $e) {
        return 0;
    }
}

// Функция проверки активных заявок на вывод
function getActiveWithdrawalRequests($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM withdrawal_requests 
            WHERE user_id = ? 
            AND status IN ('pending', 'approved')
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch();
        
        return intval($result['count']);
    } catch (Exception $e) {
        return 0;
    }
}

// Функция для логирования транзакций
function logTransaction($user_id, $type, $amount, $description, $reference_id = null, $reference_type = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO transactions 
            (user_id, type, amount, description, reference_id, reference_type, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
        ");
        
        $stmt->execute([$user_id, $type, $amount, $description, $reference_id, $reference_type]);
        return $conn->lastInsertId();
    } catch (Exception $e) {
        error_log("Transaction Log Error: " . $e->getMessage());
        return false;
    }
}

// Функция для получения настроек (если не существует в functions.php)
if (!function_exists('getSetting')) {
    function getSetting($key, $default = null) {
        global $conn;
        
        try {
            $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            
            return $result ? $result['setting_value'] : $default;
        } catch (Exception $e) {
            return $default;
        }
    }
}
?>
