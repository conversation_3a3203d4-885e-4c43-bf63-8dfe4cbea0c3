<?php
$page_title = "Вход в систему";

// Если пользователь уже авторизован, перенаправляем в личный кабинет
if (isLoggedIn()) {
    redirect('index.php?page=dashboard');
}

// Обработка формы входа
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = processLogin();
    if ($result['success']) {
        $redirect_url = $_GET['redirect'] ?? 'index.php?page=dashboard';
        redirect($redirect_url, $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt"></i> Вход в систему
                    </h3>
                    <p class="mb-0 mt-2">Добро пожаловать обратно!</p>
                </div>
                
                <div class="card-body p-4">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['verified'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Email успешно подтвержден! Теперь вы можете войти в систему.
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['reset'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Пароль успешно изменен! Войдите с новым паролем.
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="login" class="form-label">Email или имя пользователя</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="login" name="login" 
                                       value="<?php echo htmlspecialchars($_POST['login'] ?? ''); ?>" 
                                       required autocomplete="username">
                            </div>
                            <div class="invalid-feedback">
                                Введите email или имя пользователя
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Пароль</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       required autocomplete="current-password">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Введите пароль
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        Запомнить меня
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="index.php?page=forgot-password" class="text-decoration-none">
                                    Забыли пароль?
                                </a>
                            </div>
                        </div>
                        
                        <!-- Капча (простая математическая) -->
                        <?php
                        $num1 = rand(1, 10);
                        $num2 = rand(1, 10);
                        $captcha_answer = $num1 + $num2;
                        $_SESSION['captcha_answer'] = $captcha_answer;
                        ?>
                        <div class="mb-3">
                            <label for="captcha" class="form-label">
                                Решите пример: <?php echo $num1; ?> + <?php echo $num2; ?> = ?
                            </label>
                            <input type="number" class="form-control" id="captcha" name="captcha" 
                                   required min="0" max="20">
                            <div class="invalid-feedback">
                                Решите математический пример
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt"></i> Войти
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">Нет аккаунта?</p>
                        <a href="index.php?page=register" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus"></i> Зарегистрироваться
                        </a>
                    </div>
                    
                    <!-- Быстрый вход для демо -->
                    <?php if (defined('DEMO_MODE') && DEMO_MODE): ?>
                        <hr class="my-4">
                        <div class="text-center">
                            <p class="mb-2"><small class="text-muted">Демо режим:</small></p>
                            <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="fillDemoUser()">
                                Демо пользователь
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="fillDemoAdmin()">
                                Демо админ
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Информация о безопасности -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body text-center">
                    <h6 class="card-title">
                        <i class="fas fa-shield-alt text-success"></i> Ваша безопасность
                    </h6>
                    <p class="card-text small text-muted mb-0">
                        Мы используем современные технологии шифрования для защиты ваших данных. 
                        Ваши пароли хешируются и никогда не хранятся в открытом виде.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Переключение видимости пароля
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Автофокус на поле входа
    document.getElementById('login').focus();
});

<?php if (defined('DEMO_MODE') && DEMO_MODE): ?>
function fillDemoUser() {
    document.getElementById('login').value = '<EMAIL>';
    document.getElementById('password').value = 'demo123456';
    document.getElementById('captcha').value = <?php echo $captcha_answer; ?>;
}

function fillDemoAdmin() {
    document.getElementById('login').value = '<EMAIL>';
    document.getElementById('password').value = 'admin123456';
    document.getElementById('captcha').value = <?php echo $captcha_answer; ?>;
}
<?php endif; ?>
</script>

<?php
/**
 * Обработка входа в систему
 */
function processLogin() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $login = sanitizeInput($_POST['login']);
    $password = $_POST['password'];
    $captcha = intval($_POST['captcha']);
    $remember_me = isset($_POST['remember_me']);
    
    // Валидация
    if (empty($login) || empty($password)) {
        return ['success' => false, 'message' => 'Заполните все поля'];
    }
    
    // Проверка капчи
    if (!isset($_SESSION['captcha_answer']) || $captcha !== $_SESSION['captcha_answer']) {
        return ['success' => false, 'message' => 'Неверно решен пример'];
    }
    
    // Очищаем капчу
    unset($_SESSION['captcha_answer']);
    
    try {
        // Проверка лимита попыток входа
        if (!checkLoginAttempts($login)) {
            return ['success' => false, 'message' => 'Превышено количество попыток входа. Попробуйте через 15 минут.'];
        }
        
        // Поиск пользователя
        $stmt = $conn->prepare("
            SELECT id, username, email, password_hash, first_name, last_name, role, status, 
                   email_verified, balance, last_login
            FROM users 
            WHERE (email = ? OR username = ?) AND status != 'banned'
        ");
        $stmt->execute([$login, $login]);
        $user = $stmt->fetch();
        
        if (!$user) {
            recordLoginAttempt($login, false);
            return ['success' => false, 'message' => 'Неверный логин или пароль'];
        }
        
        // Проверка пароля
        if (!verifyPassword($password, $user['password_hash'])) {
            recordLoginAttempt($login, false);
            return ['success' => false, 'message' => 'Неверный логин или пароль'];
        }
        
        // Проверка статуса аккаунта
        if ($user['status'] === 'inactive') {
            return ['success' => false, 'message' => 'Аккаунт неактивен. Обратитесь в поддержку.'];
        }
        
        // Проверка подтверждения email
        if (!$user['email_verified']) {
            return ['success' => false, 'message' => 'Подтвердите email адрес. Проверьте почту.'];
        }
        
        // Успешный вход
        recordLoginAttempt($login, true);
        
        // Создание сессии
        session_regenerate_id(true);
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        
        // Установка cookie "Запомнить меня"
        if ($remember_me) {
            $token = generateToken();
            $expires = time() + (30 * 24 * 60 * 60); // 30 дней
            
            setcookie('remember_token', $token, $expires, '/', '', false, true);
            
            // Сохраняем токен в БД (можно создать отдельную таблицу remember_tokens)
            $stmt = $conn->prepare("
                UPDATE users 
                SET remember_token = ?, remember_token_expires = FROM_UNIXTIME(?)
                WHERE id = ?
            ");
            $stmt->execute([$token, $expires, $user['id']]);
        }
        
        // Обновление времени последнего входа
        $stmt = $conn->prepare("
            UPDATE users 
            SET last_login = NOW(), last_activity = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$user['id']]);
        
        // Логирование
        logAction('user_login', "User: {$user['username']}, Email: {$user['email']}");
        
        // Проверка на первый вход
        $welcome_message = $user['last_login'] ? 'Добро пожаловать!' : 'Добро пожаловать в GreenChain EcoFund!';
        
        return ['success' => true, 'message' => $welcome_message];
        
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при входе в систему. Попробуйте позже.'];
    }
}
?>
