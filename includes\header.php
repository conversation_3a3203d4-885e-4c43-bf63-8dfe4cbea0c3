<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo SITE_NAME; ?></title>
    <meta name="description" content="GreenChain EcoFund - Инвестиционная платформа для экологических проектов и экомайнинга">
    <meta name="keywords" content="экоинвестиции, зеленые технологии, экомайнинг, инвестиции, экология">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/premium-design.css">
    <link rel="stylesheet" href="assets/css/luxury-eco-design.css">
    <link rel="stylesheet" href="assets/css/luxury-backgrounds.css">
    <link rel="stylesheet" href="assets/css/luxury-icons.css">
    <link rel="stylesheet" href="assets/css/adaptive-contrast.css">
    <link rel="stylesheet" href="assets/css/white-text-system.css">
    <link rel="stylesheet" href="assets/css/mobile.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- Leaflet для карты -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <!-- Chart.js для графиков -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner">
            <div class="spinner-ring"></div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-dark luxury-navbar">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <div class="brand-logo">
                        <div class="logo-icon">🌱</div>
                        <span class="brand-text">GreenChain EcoFund</span>
                    </div>
                    <span class="brand-subtitle">Эко-инвестиционная платформа</span>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page == 'home') ? 'active' : ''; ?>" href="index.php">
                                <i class="fas fa-home"></i> Главная
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page == 'map') ? 'active' : ''; ?>" href="index.php?page=map">
                                <i class="fas fa-globe"></i> Карта проектов
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page == 'calculator') ? 'active' : ''; ?>" href="index.php?page=calculator">
                                <i class="fas fa-calculator"></i> Калькулятор
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page == 'education') ? 'active' : ''; ?>" href="index.php?page=education">
                                <i class="fas fa-graduation-cap"></i> Обучение
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page == 'leaderboard') ? 'active' : ''; ?>" href="index.php?page=leaderboard">
                                <i class="fas fa-trophy"></i> Лидерборд
                            </a>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <?php if (isLoggedIn()): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle"></i> 
                                    <?php echo sanitizeInput($_SESSION['user_name']); ?>
                                    <span class="balance-badge"><?php echo formatMoney(getUserBalance($_SESSION['user_id'])); ?></span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="index.php?page=dashboard">
                                        <i class="fas fa-tachometer-alt"></i> Личный кабинет
                                    </a></li>
                                    <li><a class="dropdown-item" href="index.php?page=invest">
                                        <i class="fas fa-chart-line"></i> Инвестировать
                                    </a></li>
                                    <li><a class="dropdown-item" href="index.php?page=withdraw">
                                        <i class="fas fa-money-bill-wave"></i> Вывести средства
                                    </a></li>
                                    <li><a class="dropdown-item" href="index.php?page=referrals">
                                        <i class="fas fa-users"></i> Рефералы
                                    </a></li>
                                    <li><a class="dropdown-item" href="index.php?page=tasks">
                                        <i class="fas fa-tasks"></i> Задания
                                    </a></li>
                                    <li><a class="dropdown-item" href="index.php?page=profile">
                                        <i class="fas fa-cog"></i> Настройки
                                    </a></li>
                                    <?php if (isAdmin()): ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="index.php?page=admin">
                                            <i class="fas fa-shield-alt"></i> Админ-панель
                                        </a></li>
                                    <?php endif; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="index.php?page=logout">
                                        <i class="fas fa-sign-out-alt"></i> Выйти
                                    </a></li>
                                </ul>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="index.php?page=login">
                                    <i class="fas fa-sign-in-alt"></i> Войти
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link btn btn-primary ms-2" href="index.php?page=register">
                                    <i class="fas fa-user-plus"></i> Регистрация
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Flash Messages -->
    <?php 
    $flash = getFlashMessage();
    if ($flash): 
    ?>
        <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Скрытый элемент с данными пользователя для JavaScript -->
    <?php if (isLoggedIn()): ?>
        <div id="user-data" style="display: none;"
             data-user-id="<?php echo $_SESSION['user_id']; ?>"
             data-user-name="<?php echo htmlspecialchars($_SESSION['user_name']); ?>"
             data-user-role="<?php echo $_SESSION['user_role']; ?>">
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
