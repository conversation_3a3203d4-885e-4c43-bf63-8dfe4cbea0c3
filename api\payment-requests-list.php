<?php
// API для получения списка платежных заявок
// GreenChain EcoFund - Payment Requests List API

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../includes/functions.php';

// Проверка авторизации
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Необходима авторизация']);
    exit;
}

try {
    $user = getCurrentUser();
    $user_id = $user['id'];
    $is_admin = ($user['role'] === 'admin');

    // Параметры запроса
    $type = $_GET['type'] ?? 'all'; // 'payment', 'withdrawal', 'all'
    $status = $_GET['status'] ?? 'all'; // 'pending', 'approved', 'rejected', 'completed', 'all'
    $limit = intval($_GET['limit'] ?? 20);
    $offset = intval($_GET['offset'] ?? 0);

    $response = [
        'success' => true,
        'data' => []
    ];

    // Получение заявок на пополнение
    if ($type === 'payment' || $type === 'all') {
        $payment_requests = getPaymentRequests($user_id, $is_admin, $status, $limit, $offset);
        $response['data']['payment_requests'] = $payment_requests;
    }

    // Получение заявок на вывод
    if ($type === 'withdrawal' || $type === 'all') {
        $withdrawal_requests = getWithdrawalRequests($user_id, $is_admin, $status, $limit, $offset);
        $response['data']['withdrawal_requests'] = $withdrawal_requests;
    }

    // Статистика для администратора
    if ($is_admin) {
        $response['data']['statistics'] = getPaymentStatistics();
    }

    echo json_encode($response);

} catch (Exception $e) {
    error_log("Payment Requests List Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Внутренняя ошибка сервера']);
}

// Функция получения заявок на пополнение
function getPaymentRequests($user_id, $is_admin, $status, $limit, $offset) {
    global $conn;

    try {
        $where_conditions = [];
        $params = [];

        // Фильтр по пользователю (если не админ)
        if (!$is_admin) {
            $where_conditions[] = "pr.user_id = ?";
            $params[] = $user_id;
        }

        // Фильтр по статусу
        if ($status !== 'all') {
            $where_conditions[] = "pr.status = ?";
            $params[] = $status;
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        $sql = "
            SELECT 
                pr.*,
                u.username,
                u.email,
                admin.username as admin_username
            FROM payment_requests pr
            LEFT JOIN users u ON pr.user_id = u.id
            LEFT JOIN users admin ON pr.admin_id = admin.id
            $where_clause
            ORDER BY pr.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("Get Payment Requests Error: " . $e->getMessage());
        return [];
    }
}

// Функция получения заявок на вывод
function getWithdrawalRequests($user_id, $is_admin, $status, $limit, $offset) {
    global $conn;

    try {
        $where_conditions = [];
        $params = [];

        // Фильтр по пользователю (если не админ)
        if (!$is_admin) {
            $where_conditions[] = "wr.user_id = ?";
            $params[] = $user_id;
        }

        // Фильтр по статусу
        if ($status !== 'all') {
            $where_conditions[] = "wr.status = ?";
            $params[] = $status;
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        $sql = "
            SELECT 
                wr.*,
                u.username,
                u.email,
                admin.username as admin_username
            FROM withdrawal_requests wr
            LEFT JOIN users u ON wr.user_id = u.id
            LEFT JOIN users admin ON wr.admin_id = admin.id
            $where_clause
            ORDER BY wr.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("Get Withdrawal Requests Error: " . $e->getMessage());
        return [];
    }
}

// Функция получения статистики платежей (для админа)
function getPaymentStatistics() {
    global $conn;

    try {
        $stats = [];

        // Статистика заявок на пополнение
        $stmt = $conn->query("
            SELECT 
                status,
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as total_amount
            FROM payment_requests 
            GROUP BY status
        ");
        $stats['payment_requests'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Статистика заявок на вывод
        $stmt = $conn->query("
            SELECT 
                status,
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as total_amount
            FROM withdrawal_requests 
            GROUP BY status
        ");
        $stats['withdrawal_requests'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Статистика за сегодня
        $stmt = $conn->query("
            SELECT 
                'payment' as type,
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as total_amount
            FROM payment_requests 
            WHERE DATE(created_at) = CURDATE()
            UNION ALL
            SELECT 
                'withdrawal' as type,
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as total_amount
            FROM withdrawal_requests 
            WHERE DATE(created_at) = CURDATE()
        ");
        $stats['today'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Ожидающие обработки
        $stmt = $conn->query("
            SELECT 
                'payment' as type,
                COUNT(*) as pending_count
            FROM payment_requests 
            WHERE status = 'pending'
            UNION ALL
            SELECT 
                'withdrawal' as type,
                COUNT(*) as pending_count
            FROM withdrawal_requests 
            WHERE status = 'pending'
        ");
        $stats['pending'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $stats;

    } catch (Exception $e) {
        error_log("Get Payment Statistics Error: " . $e->getMessage());
        return [];
    }
}
?>
